import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/models/expense.dart';
import 'package:tubewell_water_billing/models/payment.dart';
import 'package:tubewell_water_billing/repositories/repository_service.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/services/expense_service.dart';
import 'package:tubewell_water_billing/services/data_change_notifier_service.dart';
import 'package:tubewell_water_billing/widgets/date_filter_dropdown.dart';

/// Controller to manage state and business logic for the summary screen.
/// Uses ChangeNotifier for reactive UI updates and handles all summary-related operations.
class SummaryController with ChangeNotifier {
  final RepositoryService _repos = RepositoryService.instance;
  late StreamSubscription<DataChangeType> _dataChangeSubscription;

  // --- STATE ---
  bool _isLoading = true;
  bool _isRefreshing = false;

  // Date range for filtering
  DateTime? _startDate;
  DateTime? _endDate;
  DateFilterOption _selectedDateFilter = DateFilterOption.thisYear;

  // Summary data
  Map<String, double> _accountSummary = {
    'income': 0.0,
    'expenses': 0.0,
    'netBalance': 0.0,
  };

  Map<String, num> _billsSummary = {
    'totalCount': 0,
    'totalAmount': 0.0,
    'paidAmount': 0.0,
    'unpaidAmount': 0.0,
    'paidCount': 0,
    'unpaidCount': 0,
  };

  // Discount metrics
  Map<String, dynamic> _discountSummary = {
    'totalAmountDiscount': 0.0,
    'totalTimeDiscount': 0.0, // in minutes
    'billsWithAmountDiscount': 0,
    'billsWithTimeDiscount': 0,
    'averageAmountDiscount': 0.0,
    'averageTimeDiscount': 0.0, // in minutes
  };

  Map<String, double> _expenseSummary = {
    'totalAmount': 0.0,
    'count': 0.0,
    'averageAmount': 0.0,
    'maxAmount': 0.0,
    'minAmount': 0.0,
  };

  // Category breakdowns
  Map<String, double> _expenseCategories = {};
  Map<String, double> _paymentMethods = {};

  // Recent data for detailed view
  List<Bill> _recentBills = [];
  List<Payment> _recentPayments = [];
  List<Expense> _recentExpenses = [];
  Map<int, Customer> _customersMap = {};

  // --- GETTERS for UI ---
  bool get isLoading => _isLoading;
  bool get isRefreshing => _isRefreshing;
  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;
  DateFilterOption get selectedDateFilter => _selectedDateFilter;
  Map<String, double> get accountSummary => _accountSummary;
  Map<String, num> get billsSummary => _billsSummary;
  Map<String, dynamic> get discountSummary => _discountSummary;
  Map<String, double> get expenseSummary => _expenseSummary;
  Map<String, double> get expenseCategories => _expenseCategories;
  Map<String, double> get paymentMethods => _paymentMethods;
  List<Bill> get recentBills => _recentBills;
  List<Payment> get recentPayments => _recentPayments;
  List<Expense> get recentExpenses => _recentExpenses;
  Map<int, Customer> get customersMap => _customersMap;

  // --- DERIVED GETTERS ---
  double get totalBillAmount => billsSummary['totalAmount']?.toDouble() ?? 0.0;
  double get paidAmount => billsSummary['paidAmount']?.toDouble() ?? 0.0;
  double get unpaidAmount => billsSummary['unpaidAmount']?.toDouble() ?? 0.0;
  double get paymentRate => totalBillAmount > 0 ? (paidAmount / totalBillAmount) * 100 : 0.0;

  SummaryController() {
    // Initialize date range based on selected filter
    _initializeDateRange();

    // Subscribe to data changes
    _dataChangeSubscription =
        DataChangeNotifierService().onDataChanged.listen((changeType) {
      // Refresh data when relevant changes occur
      if (changeType == DataChangeType.bill ||
          changeType == DataChangeType.payment ||
          changeType == DataChangeType.expense ||
          changeType == DataChangeType.customer ||
          changeType == DataChangeType.all) {
        refresh();
      }
    });

    // Load all data initially
    loadAllData();
  }

  @override
  void dispose() {
    _dataChangeSubscription.cancel();
    super.dispose();
  }

  // --- CORE LOGIC ---
  void _initializeDateRange() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    switch (_selectedDateFilter) {
      case DateFilterOption.today:
        _startDate = today;
        _endDate = DateTime(today.year, today.month, today.day, 23, 59, 59);
        break;
      case DateFilterOption.last7Days:
        _startDate = today.subtract(const Duration(days: 6));
        _endDate = DateTime(today.year, today.month, today.day, 23, 59, 59);
        break;
      case DateFilterOption.last30Days:
        _startDate = today.subtract(const Duration(days: 29));
        _endDate = DateTime(today.year, today.month, today.day, 23, 59, 59);
        break;
      case DateFilterOption.last3Months:
        _startDate = DateTime(today.year, today.month - 3, today.day);
        // Handle year boundary
        if (_startDate!.month <= 0) {
          _startDate = DateTime(today.year - 1, 12 + _startDate!.month, today.day);
        }
        _endDate = DateTime(today.year, today.month, today.day, 23, 59, 59);
        break;
      case DateFilterOption.last6Months:
        _startDate = DateTime(today.year, today.month - 6, today.day);
        // Handle year boundary
        if (_startDate!.month <= 0) {
          _startDate = DateTime(today.year - 1, 12 + _startDate!.month, today.day);
        }
        _endDate = DateTime(today.year, today.month, today.day, 23, 59, 59);
        break;
      case DateFilterOption.thisMonth:
        _startDate = DateTime(today.year, today.month, 1);
        // Last day of current month
        final nextMonth = today.month < 12
            ? DateTime(today.year, today.month + 1, 1)
            : DateTime(today.year + 1, 1, 1);
        _endDate = nextMonth.subtract(const Duration(days: 1));
        _endDate = DateTime(_endDate!.year, _endDate!.month, _endDate!.day, 23, 59, 59);
        break;
      case DateFilterOption.lastMonth:
        final lastMonth = today.month == 1
            ? DateTime(today.year - 1, 12, 1)
            : DateTime(today.year, today.month - 1, 1);
        _startDate = lastMonth;
        // Last day of last month
        final thisMonthFirst = DateTime(today.year, today.month, 1);
        _endDate = thisMonthFirst.subtract(const Duration(days: 1));
        _endDate = DateTime(_endDate!.year, _endDate!.month, _endDate!.day, 23, 59, 59);
        break;
      case DateFilterOption.thisYear:
        _startDate = DateTime(today.year, 1, 1);
        _endDate = DateTime(today.year, 12, 31, 23, 59, 59);
        break;
      case DateFilterOption.lastYear:
        _startDate = DateTime(today.year - 1, 1, 1);
        _endDate = DateTime(today.year - 1, 12, 31, 23, 59, 59);
        break;
      case DateFilterOption.custom:
        // Default to last 30 days if custom is selected initially
        _startDate = today.subtract(const Duration(days: 30));
        _endDate = DateTime(today.year, today.month, today.day, 23, 59, 59);
        break;
    }
  }

  // --- METHODS FOR UI TO CALL ---
  Future<void> loadAllData() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Load all data in parallel for better performance
      await Future.wait([
        _loadAccountSummary(),
        _loadBillsSummary(),
        _loadDiscountSummary(),
        _loadExpenseSummary(),
        _loadRecentData(),
      ]);
    } catch (e) {
      // Handle errors
      debugPrint('Error loading summary data: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> refresh() async {
    if (_isRefreshing) return;

    _isRefreshing = true;
    notifyListeners();

    try {
      await loadAllData();
    } finally {
      _isRefreshing = false;
      notifyListeners();
    }
  }

  void onDateFilterChanged(DateFilterOption option, int tabIndex) {
    if (_selectedDateFilter == option) return;

    _selectedDateFilter = option;
    _initializeDateRange();
    loadAllData();
  }

  void onDateRangeChanged(DateTime startDate, DateTime endDate) {
    _startDate = startDate;
    _endDate = endDate;
    _selectedDateFilter = DateFilterOption.custom;
    loadAllData();
  }

  // --- PRIVATE DATA LOADING METHODS ---
  Future<void> _loadAccountSummary() async {
    final summary = await _repos.expense.getAccountSummary(
      startDate: _startDate,
      endDate: _endDate,
    );

    _accountSummary = summary;
  }

  Future<void> _loadBillsSummary() async {
    final summary = await _repos.bill.getBillsSummaryEfficient(
      startDate: _startDate,
      endDate: _endDate,
    );

    _billsSummary = summary;
  }

  Future<void> _loadDiscountSummary() async {
    try {
      // Get all bills for the date range to calculate discount metrics
      final bills = await _repos.bill.getBillsFiltered(
        startDate: _startDate,
        endDate: _endDate,
        limit: 10000, // Get all bills for accurate calculations
      );

      double totalAmountDiscount = 0.0;
      double totalTimeDiscount = 0.0; // in minutes
      int billsWithAmountDiscount = 0;
      int billsWithTimeDiscount = 0;

      for (final bill in bills) {
        if (bill.discountAmount != null && bill.discountAmount! > 0) {
          totalAmountDiscount += bill.discountAmount!;
          billsWithAmountDiscount++;
        }
        if (bill.discountTime != null && bill.discountTime! > 0) {
          totalTimeDiscount += bill.discountTime!;
          billsWithTimeDiscount++;
        }
      }

      final averageAmountDiscount = billsWithAmountDiscount > 0
          ? totalAmountDiscount / billsWithAmountDiscount
          : 0.0;
      final averageTimeDiscount = billsWithTimeDiscount > 0
          ? totalTimeDiscount / billsWithTimeDiscount
          : 0.0;

      _discountSummary = {
        'totalAmountDiscount': totalAmountDiscount,
        'totalTimeDiscount': totalTimeDiscount,
        'billsWithAmountDiscount': billsWithAmountDiscount,
        'billsWithTimeDiscount': billsWithTimeDiscount,
        'averageAmountDiscount': averageAmountDiscount,
        'averageTimeDiscount': averageTimeDiscount,
      };
    } catch (e) {
      debugPrint('Error loading discount summary: $e');
      _discountSummary = {
        'totalAmountDiscount': 0.0,
        'totalTimeDiscount': 0.0,
        'billsWithAmountDiscount': 0,
        'billsWithTimeDiscount': 0,
        'averageAmountDiscount': 0.0,
        'averageTimeDiscount': 0.0,
      };
    }
  }

  Future<void> _loadExpenseSummary() async {
    final summary = await ExpenseService.getExpenseSummary(
      startDate: _startDate,
      endDate: _endDate,
    );

    // Load expense categories breakdown
    final expenses = await _repos.expense.getAllExpenses(
      startDate: _startDate,
      endDate: _endDate,
      limit: 1000, // Get a large number to calculate accurate statistics
    );

    // Calculate category totals
    final Map<String, double> categoryTotals = {};
    for (final expense in expenses) {
      final category = expense.category;
      if (category.isNotEmpty) {
        categoryTotals[category] =
            (categoryTotals[category] ?? 0) + expense.amount;
      }
    }

    // Calculate payment method totals
    final payments = await _repos.payment.getAllPayments(
      startDate: _startDate,
      endDate: _endDate,
      limit: 1000, // Get a large number to calculate accurate statistics
    );

    final Map<String, double> methodTotals = {};
    for (final payment in payments) {
      final method = payment.paymentMethod ?? 'Unknown';
      methodTotals[method] = (methodTotals[method] ?? 0) + payment.amount;
    }

    _expenseSummary = summary;
    _expenseCategories = categoryTotals;
    _paymentMethods = methodTotals;
  }

  Future<void> _loadRecentData() async {
    // Load recent bills (last 5)
    final bills = await _repos.bill.getRecentBills(limit: 5);

    // Load recent payments (last 5)
    final payments = await _repos.payment.getRecentPayments(limit: 5);

    // Load recent expenses (last 5)
    final expenses = await _repos.expense.getRecentExpenses(limit: 5);

    // Load all customers for reference
    final customers = await _repos.customer.getAllCustomers();
    final Map<int, Customer> customersMap = {};
    for (final customer in customers) {
      customersMap[customer.id] = customer;
    }

    _recentBills = bills;
    _recentPayments = payments;
    _recentExpenses = expenses;
    _customersMap = customersMap;
  }

  /// Generate PDF report data for summary
  Future<Map<String, dynamic>> generateSummaryPdfData() async {
    try {
      return {
        'summary': true, // Flag to indicate this is a summary report
        'accountSummary': _accountSummary,
        'billSummary': _billsSummary,
        'dateRange': {
          'startDate': _startDate,
          'endDate': _endDate,
        },
        'recentBills': _recentBills,
        'recentPayments': _recentPayments,
        'recentExpenses': _recentExpenses,
        'expenseCategories': _expenseCategories,
        'paymentMethods': _paymentMethods,
        'customersMap': _customersMap,
      };
    } catch (e) {
      debugPrint('SummaryController: Error generating PDF data: $e');
      rethrow; // Let the UI handle the error display
    }
  }
  Future<String> calculateEquivalentTime() async {
    final double totalAmountDiscount = _discountSummary['totalAmountDiscount']?.toDouble() ?? 0.0;

    if (totalAmountDiscount <= 0) {
      return '0m';
    }

    try {
      // Get the default hourly rate from settings
      final double hourlyRate = await CurrencyService.getDefaultHourlyRate();

      if (hourlyRate <= 0) {
        return 'N/A';
      }

      // Convert amount discount to equivalent time in minutes
      final double equivalentTimeMinutes = (totalAmountDiscount / hourlyRate) * 60;
      final int hours = (equivalentTimeMinutes / 60).floor();
      final int minutes = (equivalentTimeMinutes % 60).round();

      if (hours > 0) {
        return '${hours}h ${minutes}m';
      } else {
        return '${minutes}m';
      }
    } catch (e) {
      return 'N/A';
    }
  }

  Future<String> calculateEquivalentAmount() async {
    final double totalTimeDiscount = _discountSummary['totalTimeDiscount']?.toDouble() ?? 0.0;

    if (totalTimeDiscount <= 0) {
      return CurrencyService.formatCurrency(0.0);
    }

    try {
      // Get the default hourly rate from settings
      final double hourlyRate = await CurrencyService.getDefaultHourlyRate();

      if (hourlyRate <= 0) {
        return 'N/A';
      }

      // Convert time discount (in minutes) to equivalent amount
      final double equivalentAmount = (totalTimeDiscount / 60) * hourlyRate;

      return CurrencyService.formatCurrency(equivalentAmount);
    } catch (e) {
      return 'N/A';
    }
  }
}
