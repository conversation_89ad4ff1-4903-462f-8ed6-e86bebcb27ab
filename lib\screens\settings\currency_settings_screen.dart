import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tubewell_water_billing/controllers/currency_settings_controller.dart';
import 'package:tubewell_water_billing/models/currency.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';

class CurrencySettingsScreen extends StatelessWidget {
  const CurrencySettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => CurrencySettingsController(),
      child: const _CurrencySettingsView(),
    );
  }
}

class _CurrencySettingsView extends StatelessWidget {
  const _CurrencySettingsView();

  @override
  Widget build(BuildContext context) {
    final controller = Provider.of<CurrencySettingsController>(context);

    return Scaffold(
      appBar: const TubewellAppBar(
        title: 'Currency Settings',
        showPdfOption: false,
      ),
      body: controller.isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              padding: const EdgeInsets.all(16.0),
              children: [
                _buildCurrencySettings(context, controller),
                const SizedBox(height: 20),
                _buildHourlyRateSettings(context, controller),
                const SizedBox(height: 20),
                _buildCurrencyPreview(context, controller),
                const SizedBox(height: 20),
                _buildSaveButton(context, controller),
              ],
            ),
    );
  }

  Widget _buildCurrencySettings(BuildContext context, CurrencySettingsController controller) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Select Currency', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            const Text('Select your preferred currency that will be used throughout the app.', style: TextStyle(color: Colors.grey)),
            const SizedBox(height: 16),
            DropdownButtonFormField<Currency>(
              value: controller.selectedCurrency,
              decoration: const InputDecoration(labelText: 'Currency', border: OutlineInputBorder(), contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12)),
              items: controller.availableCurrencies.map((currency) {
                return DropdownMenuItem<Currency>(
                  value: currency,
                  child: Row(
                    children: [
                      Text(currency.symbol, style: const TextStyle(fontSize: 18)),
                      const SizedBox(width: 12),
                      Text(currency.name),
                      const SizedBox(width: 8),
                      Text('(${currency.code})', style: TextStyle(color: Colors.grey.shade600, fontSize: 12)),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (currency) {
                if (currency != null) {
                  controller.setSelectedCurrency(currency);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHourlyRateSettings(BuildContext context, CurrencySettingsController controller) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Default Hourly Rate', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            const Text('Set the default hourly rate for new transactions. This rate will be auto-populated in the transaction form.', style: TextStyle(color: Colors.grey)),
            const SizedBox(height: 16),
            TextFormField(
              controller: controller.hourlyRateController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'Hourly Rate (${controller.selectedCurrency.symbol})',
                hintText: 'Enter your default charging rate per hour',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: BorderSide(color: Colors.grey.shade400)),
                focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: BorderSide(color: Colors.green.shade700, width: 2)),
                filled: true,
                fillColor: Colors.green.shade50,
                prefixIcon: Icon(Icons.attach_money, color: Colors.green.shade700),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                helperText: 'This rate will be pre-filled when creating new transactions',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) return 'Please enter an hourly rate';
                if (double.tryParse(value) == null) return 'Please enter a valid number';
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrencyPreview(BuildContext context, CurrencySettingsController controller) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Currency Preview', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            const Text('This is how the selected currency will be displayed throughout the app.', style: TextStyle(color: Colors.grey)),
            const SizedBox(height: 16),
            Card(
              color: Colors.grey.shade100,
              elevation: 0,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Symbol: ${controller.selectedCurrency.symbol}', style: const TextStyle(fontSize: 16)),
                    const SizedBox(height: 8),
                    Text('Name: ${controller.selectedCurrency.name}', style: const TextStyle(fontSize: 16)),
                    const SizedBox(height: 8),
                    Text('Code: ${controller.selectedCurrency.code}', style: const TextStyle(fontSize: 16)),
                    const SizedBox(height: 8),
                    Text('Country Code: ${controller.selectedCurrency.countryCode} (for WhatsApp messages)', style: const TextStyle(fontSize: 16)),
                    const SizedBox(height: 8),
                    Text('Default Hourly Rate: ${controller.selectedCurrency.symbol} ${controller.hourlyRateController.text}', style: const TextStyle(fontSize: 16)),
                    const SizedBox(height: 16),
                    const Text('Example Amounts:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Text('Small: ${CurrencyService.formatCurrency(100, decimalPlaces: 2)}', style: const TextStyle(fontSize: 16)),
                    const SizedBox(height: 4),
                    Text('Medium: ${CurrencyService.formatCurrency(1000, decimalPlaces: 2)}', style: const TextStyle(fontSize: 16)),
                    const SizedBox(height: 4),
                    Text('Large: ${CurrencyService.formatCurrency(10000, decimalPlaces: 2)}', style: const TextStyle(fontSize: 16)),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton(BuildContext context, CurrencySettingsController controller) {
    return ElevatedButton(
      onPressed: () async {
        final success = await controller.saveSettings();
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(success ? 'Settings saved successfully' : 'Error saving settings'),
              backgroundColor: success ? Colors.green : Colors.red,
            ),
          );
          if (success) {
            Navigator.pop(context, true);
          }
        }
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      child: const Text('Save Currency Settings', style: TextStyle(fontSize: 16)),
    );
  }
}
