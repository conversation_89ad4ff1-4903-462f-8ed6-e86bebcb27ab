import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tubewell_water_billing/controllers/customer_detail_controller.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing/forms/transaction_form_screen.dart';
import 'package:tubewell_water_billing/forms/auto_payment_form_screen.dart';
import 'package:tubewell_water_billing/utils/navigation_helper.dart';
import 'package:tubewell_water_billing/screens/customer_detail/summary_tab.dart';
import 'package:tubewell_water_billing/screens/customer_detail/transactions_tab.dart';
import 'package:tubewell_water_billing/screens/customer_detail/payments_tab.dart';

class CustomerDetailScreen extends StatelessWidget {
  final Customer customer;

  const CustomerDetailScreen({
    super.key,
    required this.customer,
  });

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => CustomerDetailController(
        customer: customer,
        vsync: context.findAncestorStateOfType<TickerProvider>()!,
      ),
      child: _CustomerDetailView(customer: customer),
    );
  }
}

class _CustomerDetailView extends StatefulWidget {
  final Customer customer;
  const _CustomerDetailView({required this.customer});

  @override
  State<_CustomerDetailView> createState() => _CustomerDetailViewState();
}

class _CustomerDetailViewState extends State<_CustomerDetailView> with TickerProviderStateMixin {
  late CustomerDetailController _controller;

  @override
  void initState() {
    super.initState();
    _controller = CustomerDetailController(customer: widget.customer, vsync: this);
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Widget _buildCustomTab(BuildContext context, String text, IconData icon, int tabIndex, Color inactiveColor) {
    final controller = Provider.of<CustomerDetailController>(context);
    final bool isSelected = controller.tabController.index == tabIndex;
    final Color textColor = isSelected ? Theme.of(context).primaryColor : inactiveColor;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(icon, color: textColor, size: 20),
        const SizedBox(height: 4),
        Text(
          text,
          style: TextStyle(color: textColor, fontSize: 12, fontWeight: isSelected ? FontWeight.bold : FontWeight.normal),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CustomerDetailController>(
      builder: (context, controller, child) {
        return Scaffold(
          appBar: TubewellAppBar(
            title: controller.customer.name,
            showBackButton: true,
            onPdfPressed: () => controller.handlePdfGeneration(context),
          ),
          body: Column(
            children: [
              PreferredSize(
                preferredSize: const Size.fromHeight(60.0),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(bottom: BorderSide(color: Colors.blue.shade200, width: 1)),
                  ),
                  child: TabBar(
                    controller: controller.tabController,
                    labelColor: Theme.of(context).primaryColor,
                    unselectedLabelColor: Colors.transparent,
                    indicatorColor: Theme.of(context).primaryColor,
                    indicatorWeight: 3.0,
                    tabs: [
                      Tab(child: _buildCustomTab(context, 'SUMMARY', Icons.analytics, 0, Colors.teal.shade600)),
                      Tab(child: _buildCustomTab(context, 'BILLS', Icons.receipt_long, 1, Colors.purple.shade600)),
                      Tab(child: _buildCustomTab(context, 'PAYMENTS', Icons.payments, 2, Colors.indigo.shade600)),
                    ],
                  ),
                ),
              ),
              Expanded(
                child: TabBarView(
                  controller: controller.tabController,
                  children: [
                    CustomerSummaryTab(
                      key: controller.summaryTabKey,
                      customer: controller.customer,
                      onDataChanged: controller.loadCustomerSummary,
                      totalBilledAmount: controller.totalBilledAmount,
                      totalPaidAmount: controller.totalPaidAmount,
                      netBalance: controller.netBalance,
                      isLoadingSummary: controller.isLoadingSummary,
                      onReminderPressed: () => controller.showReminderDialog(context),
                      onRefreshPressed: controller.loadCustomerSummary,
                    ),
                    CustomerTransactionsTab(
                      key: controller.transactionsTabKey,
                      customer: controller.customer,
                      onDataChanged: controller.loadCustomerSummary,
                    ),
                    CustomerPaymentsTab(
                      key: controller.paymentsTabKey,
                      customer: controller.customer,
                      onDataChanged: controller.loadCustomerSummary,
                    ),
                  ],
                ),
              ),
            ],
          ),
          floatingActionButton: controller.tabController.index == 0 ? null : FloatingActionButton(
            heroTag: 'fab-customer-detail-${controller.tabController.index}',
            backgroundColor: const Color(0xFF2E7D32),
            onPressed: () {
              if (controller.tabController.index == 1) {
                NavigationHelper.navigateWithSlide(context, TransactionFormScreen(selectedCustomer: controller.customer)).then((result) {
                  if (result == true) {
                    controller.refreshAllTabs();
                    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Transaction added successfully'), backgroundColor: Color(0xFF2E7D32), duration: Duration(seconds: 2)));
                  }
                });
              } else {
                NavigationHelper.navigateWithSlide(context, AutoPaymentFormScreen(customer: controller.customer)).then((result) {
                  if (result == true) {
                    controller.refreshAllTabs();
                    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Payment added successfully'), backgroundColor: Color(0xFF2E7D32), duration: Duration(seconds: 2)));
                  }
                });
              }
            },
            child: const Icon(Icons.add, color: Colors.white),
          ),
        );
      },
    );
  }
}
