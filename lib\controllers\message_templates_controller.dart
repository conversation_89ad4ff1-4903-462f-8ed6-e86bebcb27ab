import 'package:flutter/material.dart';
import 'package:tubewell_water_billing/services/message_service.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/payment.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/services/settings_service.dart';

class MessageTemplatesController with ChangeNotifier {
  bool _isLoading = true;
  late TabController tabController;

  final TextEditingController billTemplateController = TextEditingController();
  final TextEditingController paymentTemplateController = TextEditingController();
  final TextEditingController reminderTemplateController = TextEditingController();

  final Customer _sampleCustomer = Customer(id: 0, name: '<PERSON>', contactNumber: '+919876543210', createdAt: DateTime.now());
  late Bill _sampleBill;
  late Payment _samplePayment;

  String _defaultBillTemplate = '';
  String _defaultPaymentTemplate = '';
  String _defaultReminderTemplate = '';

  String? _customBillTemplate;
  String? _customPaymentTemplate;
  String? _customReminderTemplate;

  // Getters
  bool get isLoading => _isLoading;

  MessageTemplatesController({required TickerProvider vsync}) {
    tabController = TabController(length: 3, vsync: vsync);
    _initializeSampleData();
    loadTemplates();
  }

  void _initializeSampleData() {
    _sampleBill = Bill(id: 1001, customerId: _sampleCustomer.id, billDate: DateTime.now(), startTime: DateTime.now().subtract(const Duration(days: 1)), endTime: DateTime.now(), durationHours: 24, durationHoursWhole: 24, durationMinutes: 0, hourlyRate: 50, amount: 1200, isPaid: false, isPartiallyPaid: false, partialAmount: 0);
    _samplePayment = Payment(id: 2001, customerId: _sampleCustomer.id, billId: _sampleBill.id, amount: 1000, paymentDate: DateTime.now(), paymentMethod: 'Cash', remarks: 'Regular payment');
  }

  Future<void> loadTemplates() async {
    _isLoading = true;
    notifyListeners();

    try {
      await SettingsService.initialize();
      final accountId = DatabaseService.getCurrentAccountId() ?? '';

      _customBillTemplate = await SettingsService.getMessageTemplate(accountId: accountId, templateType: 'bill');
      _customPaymentTemplate = await SettingsService.getMessageTemplate(accountId: accountId, templateType: 'payment');
      _customReminderTemplate = await SettingsService.getMessageTemplate(accountId: accountId, templateType: 'reminder');

      _defaultBillTemplate = MessageService.getDefaultBillTemplate();
      _defaultPaymentTemplate = MessageService.getDefaultPaymentTemplate();
      _defaultReminderTemplate = MessageService.getDefaultReminderTemplate();

      billTemplateController.text = _customBillTemplate ?? _defaultBillTemplate;
      paymentTemplateController.text = _customPaymentTemplate ?? _defaultPaymentTemplate;
      reminderTemplateController.text = _customReminderTemplate ?? _defaultReminderTemplate;
    } catch (e) {
      // Handle error
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> saveTemplate(String templateType, String template) async {
    _isLoading = true;
    notifyListeners();
    try {
      final accountId = DatabaseService.getCurrentAccountId() ?? '';
      await SettingsService.saveMessageTemplate(accountId: accountId, templateType: templateType, template: template);
      switch (templateType) {
        case 'bill':
          _customBillTemplate = template;
          break;
        case 'payment':
          _customPaymentTemplate = template;
          break;
        case 'reminder':
          _customReminderTemplate = template;
          break;
      }
      return true;
    } catch (e) {
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> resetTemplate(String templateType) async {
    _isLoading = true;
    notifyListeners();
    try {
      final accountId = DatabaseService.getCurrentAccountId() ?? '';
      await SettingsService.deleteMessageTemplate(accountId: accountId, templateType: templateType);
      switch (templateType) {
        case 'bill':
          _customBillTemplate = null;
          billTemplateController.text = _defaultBillTemplate;
          break;
        case 'payment':
          _customPaymentTemplate = null;
          paymentTemplateController.text = _defaultPaymentTemplate;
          break;
        case 'reminder':
          _customReminderTemplate = null;
          reminderTemplateController.text = _defaultReminderTemplate;
          break;
      }
      return true;
    } catch (e) {
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  String getPreviewForTemplate(String templateType, String template) {
    try {
      switch (templateType) {
        case 'bill':
          return MessageService.formatBillMessageWithTemplate(_sampleBill, _sampleCustomer, template);
        case 'payment':
          return MessageService.formatPaymentMessageWithTemplate(_samplePayment, _sampleCustomer, template, remainingCredit: 200);
        case 'reminder':
          return MessageService.formatReminderMessageWithTemplate([_sampleBill], _sampleCustomer, template);
        default:
          return 'Preview not available';
      }
    } catch (e) {
      return 'Error generating preview: $e';
    }
  }

  @override
  void dispose() {
    tabController.dispose();
    billTemplateController.dispose();
    paymentTemplateController.dispose();
    reminderTemplateController.dispose();
    super.dispose();
  }
}
