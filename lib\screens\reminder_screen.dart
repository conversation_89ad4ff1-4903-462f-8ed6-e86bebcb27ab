import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:tubewell_water_billing/controllers/reminder_controller.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing/widgets/app_drawer.dart';
import 'package:tubewell_water_billing/widgets/empty_state_widget.dart';
import 'package:tubewell_water_billing/widgets/reminder_dialog.dart';
import 'package:tubewell_water_billing/screens/bill_details_screen.dart';

class ReminderScreen extends StatelessWidget {
  const ReminderScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ReminderController(),
      child: const _ReminderView(),
    );
  }
}

class _ReminderView extends StatefulWidget {
  const _ReminderView();

  @override
  State<_ReminderView> createState() => _ReminderViewState();
}

class _ReminderViewState extends State<_ReminderView> with AutomaticKeepAliveClientMixin {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    final controller = Provider.of<ReminderController>(context, listen: false);
    _searchController.addListener(() => controller.onSearchChanged(_searchController.text));
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _showFilterDialog(BuildContext context, ReminderController controller) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Filter Options'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CheckboxListTile(
              title: const Text('Show Partially Paid Bills'),
              value: controller.showPartiallyPaid,
              onChanged: (value) {
                controller.setFilter(showPartiallyPaid: value ?? true);
                Navigator.pop(dialogContext);
              },
            ),
            CheckboxListTile(
              title: const Text('Show Fully Unpaid Bills'),
              value: controller.showFullyUnpaid,
              onChanged: (value) {
                controller.setFilter(showFullyUnpaid: value ?? true);
                Navigator.pop(dialogContext);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: const Text('CLOSE'),
          ),
        ],
      ),
    );
  }

  void _showSortDialog(BuildContext context, ReminderController controller) {
    String tempSortBy = controller.sortBy;
    bool tempSortAscending = controller.sortAscending;

    showDialog(
      context: context,
      builder: (dialogContext) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: const Text('Sort By'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<String>(
                  title: const Text('Date'),
                  value: 'date',
                  groupValue: tempSortBy,
                  onChanged: (value) => setState(() => tempSortBy = value!),
                ),
                RadioListTile<String>(
                  title: const Text('Amount'),
                  value: 'amount',
                  groupValue: tempSortBy,
                  onChanged: (value) => setState(() => tempSortBy = value!),
                ),
                RadioListTile<String>(
                  title: const Text('Customer Name'),
                  value: 'customer',
                  groupValue: tempSortBy,
                  onChanged: (value) => setState(() => tempSortBy = value!),
                ),
                const Divider(),
                SwitchListTile(
                  title: const Text('Ascending Order'),
                  value: tempSortAscending,
                  onChanged: (value) => setState(() => tempSortAscending = value),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(dialogContext),
                child: const Text('CANCEL'),
              ),
              TextButton(
                onPressed: () {
                  controller.setSort(sortBy: tempSortBy, sortAscending: tempSortAscending);
                  Navigator.pop(dialogContext);
                },
                child: const Text('APPLY'),
              ),
            ],
          );
        },
      ),
    );
  }

  void _showReminderDialog(BuildContext context, Customer customer, List<Bill> bills) {
    showDialog(
      context: context,
      builder: (dialogContext) => ReminderDialog(
        customer: customer,
        bills: bills,
        title: 'Send Payment Reminder',
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final controller = Provider.of<ReminderController>(context);

    return Scaffold(
      appBar: const TubewellAppBar(
        title: 'Payment Reminders',
        showBackButton: true,
      ),
      drawer: const AppDrawer(currentScreen: 'reminders'),
      body: _buildContent(controller),
    );
  }

  Widget _buildContent(ReminderController controller) {
    if (controller.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    final filteredEntries = controller.filteredAndSortedCustomerBills;

    if (filteredEntries.isEmpty && controller.searchQuery.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.notifications_none,
        title: 'No Unpaid Bills',
        message: 'There are no unpaid bills to send reminders for.',
      );
    }
    
    if (filteredEntries.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.filter_alt,
        title: 'No Matching Bills',
        message: 'No bills match your current filter settings.',
      );
    }

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    hintText: 'Search customers...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.filter_list),
                onPressed: () => _showFilterDialog(context, controller),
                tooltip: 'Filter',
              ),
              IconButton(
                icon: const Icon(Icons.sort),
                onPressed: () => _showSortDialog(context, controller),
                tooltip: 'Sort',
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: filteredEntries.length,
            itemBuilder: (context, index) {
              final entry = filteredEntries[index];
              final customerId = entry.key;
              final bills = entry.value;
              final customer = controller.customersMap[customerId]!;
              final totalOutstanding = bills.fold<double>(0, (sum, bill) => sum + bill.outstandingAmount);
              return _buildCustomerCard(context, customer, bills, totalOutstanding);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCustomerCard(BuildContext context, Customer customer, List<Bill> bills, double totalOutstanding) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: ExpansionTile(
        title: Text(customer.name, style: const TextStyle(fontWeight: FontWeight.bold)),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${bills.length} unpaid ${bills.length == 1 ? 'bill' : 'bills'}'),
            Text(
              'Total: ${CurrencyService.formatCurrency(totalOutstanding)}',
              style: TextStyle(color: Colors.red.shade700, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        leading: CircleAvatar(
          backgroundColor: Colors.blue.shade700,
          child: Text(customer.name.isNotEmpty ? customer.name[0].toUpperCase() : '?', style: const TextStyle(color: Colors.white)),
        ),
        trailing: IconButton(
          icon: const Icon(Icons.send),
          onPressed: () => _showReminderDialog(context, customer, bills),
          tooltip: 'Send Reminder',
        ),
        children: bills.map((bill) => _buildBillItem(context, bill, customer)).toList(),
      ),
    );
  }

  Widget _buildBillItem(BuildContext context, Bill bill, Customer customer) {
    final dateFormat = DateFormat('dd MMM yyyy');
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16),
      title: Text('Bill #${bill.id}'),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Date: ${dateFormat.format(bill.billDate)}'),
          Text(
            bill.isPartiallyPaid ? 'Partially Paid: ${CurrencyService.formatCurrency(bill.outstandingAmount)}' : 'Amount: ${CurrencyService.formatCurrency(bill.amount)}',
            style: TextStyle(color: bill.isPartiallyPaid ? Colors.blue.shade700 : Colors.red.shade700, fontWeight: FontWeight.bold),
          ),
        ],
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            icon: const Icon(Icons.visibility),
            onPressed: () {
              Navigator.push(context, MaterialPageRoute(builder: (context) => BillDetailsScreen(bill: bill, customer: customer)));
            },
            tooltip: 'View Details',
          ),
          IconButton(
            icon: const Icon(Icons.send),
            onPressed: () => _showReminderDialog(context, customer, [bill]),
            tooltip: 'Send Reminder for this Bill',
          ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
