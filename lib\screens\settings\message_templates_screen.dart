import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:tubewell_water_billing/controllers/message_templates_controller.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';

class MessageTemplatesScreen extends StatelessWidget {
  const MessageTemplatesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => MessageTemplatesController(vsync: context.findAncestorStateOfType<TickerProvider>()!),
      child: const _MessageTemplatesView(),
    );
  }
}

class _MessageTemplatesView extends StatefulWidget {
  const _MessageTemplatesView();

  @override
  State<_MessageTemplatesView> createState() => _MessageTemplatesViewState();
}

class _MessageTemplatesViewState extends State<_MessageTemplatesView> with TickerProviderStateMixin {
  late MessageTemplatesController _controller;

  @override
  void initState() {
    super.initState();
    _controller = MessageTemplatesController(vsync: this);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<MessageTemplatesController>(
      builder: (context, controller, child) {
        return Scaffold(
          appBar: const TubewellAppBar(title: 'Message Templates', showPdfOption: false),
          body: controller.isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                  children: [
                    TabBar(
                      controller: controller.tabController,
                      labelColor: Theme.of(context).primaryColor,
                      unselectedLabelColor: Colors.grey,
                      indicatorColor: Theme.of(context).primaryColor,
                      tabs: const [
                        Tab(text: 'Bill', icon: Icon(Icons.receipt)),
                        Tab(text: 'Payment', icon: Icon(Icons.payment)),
                        Tab(text: 'Reminder', icon: Icon(Icons.notifications)),
                      ],
                    ),
                    Expanded(
                      child: TabBarView(
                        controller: controller.tabController,
                        children: [
                          _buildTemplateTab(context, controller, 'bill', 'Bill Notification Template', 'Customize the message sent to customers when a new bill is generated.', ['{customer_name}', '{bill_id}', '{bill_date}', '{start_date}', '{end_date}', '{duration}', '{amount}', '{status}', '{paid_amount}', '{remaining_amount}']),
                          _buildTemplateTab(context, controller, 'payment', 'Payment Notification Template', 'Customize the message sent to customers when a payment is recorded.', ['{customer_name}', '{payment_id}', '{payment_date}', '{amount}', '{payment_method}', '{bill_id}', '{remaining_credit}']),
                          _buildTemplateTab(context, controller, 'reminder', 'Payment Reminder Template', 'Customize the message sent to customers as a reminder for unpaid bills.', ['{customer_name}', '{bill_id}', '{bill_date}', '{amount}', '{status}', '{remaining_amount}', '{total_amount}']),
                        ],
                      ),
                    ),
                  ],
                ),
        );
      },
    );
  }

  Widget _buildTemplateTab(BuildContext context, MessageTemplatesController controller, String templateType, String title, String description, List<String> placeholders) {
    final textController = templateType == 'bill' ? controller.billTemplateController : templateType == 'payment' ? controller.paymentTemplateController : controller.reminderTemplateController;
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(title, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  Text(description, style: TextStyle(color: Colors.grey.shade600)),
                  const SizedBox(height: 16),
                  const Text('Available Placeholders:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: placeholders.map((placeholder) {
                      return InkWell(
                        onTap: () {
                          final currentPosition = textController.selection.baseOffset;
                          final text = textController.text;
                          final newText = text.substring(0, currentPosition) + placeholder + text.substring(currentPosition);
                          textController.text = newText;
                          textController.selection = TextSelection.fromPosition(TextPosition(offset: currentPosition + placeholder.length));
                        },
                        child: Chip(label: Text(placeholder), backgroundColor: Colors.blue.shade50, side: BorderSide(color: Colors.blue.shade200)),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Template Editor', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                  const SizedBox(height: 16),
                  TextField(controller: textController, maxLines: 10, decoration: const InputDecoration(border: OutlineInputBorder(), hintText: 'Enter your message template here...')),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(flex: 1, child: Padding(padding: const EdgeInsets.only(right: 8.0), child: OutlinedButton.icon(onPressed: () async {
                        final success = await controller.resetTemplate(templateType);
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(success ? 'Template reset to default' : 'Error resetting template'), backgroundColor: success ? Colors.green : Colors.red));
                        }
                      }, icon: const Icon(Icons.refresh, size: 18), label: const Text('Reset')))),
                      Flexible(flex: 1, child: ElevatedButton.icon(onPressed: () async {
                        final success = await controller.saveTemplate(templateType, textController.text);
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(success ? 'Template saved successfully' : 'Error saving template'), backgroundColor: success ? Colors.green : Colors.red));
                        }
                      }, icon: const Icon(Icons.save, size: 18), label: const Text('Save'), style: ElevatedButton.styleFrom(backgroundColor: Colors.green, foregroundColor: Colors.white))),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Message Preview', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      IconButton(
                        icon: const Icon(Icons.copy),
                        onPressed: () {
                          final preview = controller.getPreviewForTemplate(templateType, textController.text);
                          Clipboard.setData(ClipboardData(text: preview));
                          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Preview copied to clipboard')));
                        },
                        tooltip: 'Copy to clipboard',
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(color: Colors.grey.shade100, borderRadius: BorderRadius.circular(8), border: Border.all(color: Colors.grey.shade300)),
                    child: Text(controller.getPreviewForTemplate(templateType, textController.text)),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
