import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/repositories/account_filtering_mixin.dart';

/// Repository for customer-related database operations
class CustomerRepository with AccountFilteringMixin {
  final DatabaseService _dbService = DatabaseService.instance;

  /// Get a customer by ID
  Future<Customer?> getCustomerById(int id) async {
    final db = await _dbService.database;
    final maps = await db.query(
      'customers',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Customer.fromMap(maps.first);
    }
    return null;
  }

  /// Get all customers with optional search
  Future<List<Customer>> getAllCustomers({String? searchQuery}) async {
    final db = await _dbService.database;

    Map<String, dynamic> filter;
    if (searchQuery != null && searchQuery.isNotEmpty) {
      filter = buildAccountFilter(
        additionalWhere: 'name LIKE ?',
        additionalArgs: ['%$searchQuery%'],
      );
    } else {
      filter = buildAccountFilter();
    }

    final maps = await db.query(
      'customers',
      where: filter['where'],
      whereArgs: filter['whereArgs'],
      orderBy: 'name ASC',
    );

    return maps.map((map) => Customer.fromMap(map)).toList();
  }

  /// Get customers with pagination and search support
  ///
  /// Note: This method is deprecated in favor of getCustomersWithDetails
  /// which provides better performance and type safety.
  @Deprecated('Use getCustomersWithDetails instead for better performance and type safety')
  Future<List<Customer>> getCustomersPaginated({
    int page = 0,
    int pageSize = 20,
    String? searchQuery,
    CustomerSortOption? sortOption,
  }) async {
    final db = await _dbService.database;
    final offset = page * pageSize;

    String orderBy = 'name ASC'; // Default sort
    switch (sortOption) {
      case CustomerSortOption.nameDesc:
        orderBy = 'name DESC';
        break;
      case CustomerSortOption.creditDesc:
        orderBy = 'balance DESC';
        break;
      case CustomerSortOption.dueDesc:
        orderBy = 'balance ASC';
        break;
      case CustomerSortOption.nameAsc:
      case null:
        orderBy = 'name ASC';
        break;
    }

    Map<String, dynamic> filter;
    if (searchQuery != null && searchQuery.isNotEmpty) {
      filter = buildAccountFilter(
        additionalWhere: 'name LIKE ?',
        additionalArgs: ['%$searchQuery%'],
      );
    } else {
      filter = buildAccountFilter();
    }

    final maps = await db.query(
      'customers',
      where: filter['where'],
      whereArgs: filter['whereArgs'],
      orderBy: orderBy,
      limit: pageSize,
      offset: offset,
    );

    return maps.map((map) => Customer.fromMap(map)).toList();
  }

  /// Save a customer (insert or update)
  Future<int> saveCustomer(Customer customer) async {
    final db = await _dbService.database;

    // Add the current account ID to the customer data
    final customerData = customer.toMap();
    customerData['accountId'] = _currentAccountId;

    if (customer.id == 0) {
      // Insert new customer
      return await db.insert('customers', customerData);
    } else {
      // Update existing customer
      await db.update(
        'customers',
        customerData,
        where: 'id = ?',
        whereArgs: [customer.id],
      );
      return customer.id;
    }
  }

  /// Update a customer
  Future<void> updateCustomer(Customer customer) async {
    final db = await _dbService.database;
    final customerData = customer.toMap();
    customerData['accountId'] = _currentAccountId;

    await db.update(
      'customers',
      customerData,
      where: 'id = ?',
      whereArgs: [customer.id],
    );
  }

  /// Delete a customer
  Future<void> deleteCustomer(int id) async {
    final db = await _dbService.database;
    await db.delete(
      'customers',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Get customers by IDs (for efficient batch loading)
  Future<List<Customer>> getCustomersByIds(List<int> ids) async {
    if (ids.isEmpty) return [];
    
    final db = await _dbService.database;
    final placeholders = ids.map((_) => '?').join(',');
    final maps = await db.query(
      'customers',
      where: 'id IN ($placeholders)',
      whereArgs: ids,
    );

    return maps.map((map) => Customer.fromMap(map)).toList();
  }

  /// Get customer count for pagination
  Future<int> getCustomerCount({String? searchQuery}) async {
    final db = await _dbService.database;
    
    String whereClause;
    List<dynamic> whereArgs;
    
    if (_currentAccountId != null) {
      if (searchQuery != null && searchQuery.isNotEmpty) {
        whereClause = '(accountId = ? OR accountId IS NULL) AND name LIKE ?';
        whereArgs = [_currentAccountId, '%$searchQuery%'];
      } else {
        whereClause = 'accountId = ? OR accountId IS NULL';
        whereArgs = [_currentAccountId];
      }
    } else {
      if (searchQuery != null && searchQuery.isNotEmpty) {
        whereClause = 'accountId IS NULL AND name LIKE ?';
        whereArgs = ['%$searchQuery%'];
      } else {
        whereClause = 'accountId IS NULL';
        whereArgs = [];
      }
    }

    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM customers WHERE $whereClause',
      whereArgs,
    );

    return result.first['count'] as int;
  }

  /// Update customer balance
  Future<void> updateCustomerBalance(int customerId, double balance) async {
    final db = await _dbService.database;
    await db.update(
      'customers',
      {'balance': balance},
      where: 'id = ?',
      whereArgs: [customerId],
    );
  }

  /// Get customers with non-zero balances
  Future<List<Customer>> getCustomersWithBalance() async {
    final db = await _dbService.database;
    List<Map<String, dynamic>> maps;

    if (_currentAccountId != null) {
      maps = await db.query(
        'customers',
        where: '(accountId = ? OR accountId IS NULL) AND balance != 0',
        whereArgs: [_currentAccountId],
        orderBy: 'balance DESC',
      );
    } else {
      maps = await db.query(
        'customers',
        where: 'accountId IS NULL AND balance != 0',
        orderBy: 'balance DESC',
      );
    }

    return maps.map((map) => Customer.fromMap(map)).toList();
  }

  /// **OPTIMIZED METHOD** - Get customers with pre-calculated balance details
  /// This method performs all calculations in a single, efficient SQL query with JOINs
  /// and handles pagination, sorting, and filtering at the database level.
  Future<Map<String, dynamic>> getCustomersWithDetails({
    required int page,
    required int pageSize,
    String? searchQuery,
    CustomerSortOption? sortOption,
  }) async {
    final db = await _dbService.database;
    final offset = page * pageSize;

    // --- 1. Build the Main Query with JOINs and Subqueries ---
    // This query calculates each customer's balance on-the-fly using the same logic
    // as the database migration: credit_amount - outstanding_amount
    String baseQuery = '''
      SELECT
        c.*,
        COALESCE(credits.credit_amount, 0.0) as credit_amount,
        COALESCE(bills.outstanding_amount, 0.0) as outstanding_amount,
        (COALESCE(credits.credit_amount, 0.0) - COALESCE(bills.outstanding_amount, 0.0)) as calculated_balance
      FROM customers c
      LEFT JOIN (
        SELECT
          customerId,
          amount as credit_amount
        FROM customer_credits
      ) credits ON c.id = credits.customerId
      LEFT JOIN (
        SELECT
          customerId,
          SUM(amount - COALESCE(partialAmount, 0.0)) as outstanding_amount
        FROM bills
        WHERE isPaid = 0
        GROUP BY customerId
      ) bills ON c.id = bills.customerId
    ''';

    // --- 2. Build WHERE clause for account filtering and searching ---
    List<String> whereConditions = [];
    List<dynamic> whereArgs = [];

    // Always filter by account
    whereConditions.add(buildAccountFilterForRawQuery('c', whereArgs).substring(5)); // Remove ' AND ' prefix

    // Add search filter if provided
    if (searchQuery != null && searchQuery.isNotEmpty) {
      whereConditions.add('c.name LIKE ?');
      whereArgs.add('%$searchQuery%');
    }

    if (whereConditions.isNotEmpty) {
      baseQuery += ' WHERE ${whereConditions.join(' AND ')}';
    }

    // --- 3. Build ORDER BY clause for sorting ---
    String orderBy = ' ORDER BY ';
    switch (sortOption) {
      case CustomerSortOption.nameDesc:
        orderBy += 'c.name DESC';
        break;
      case CustomerSortOption.creditDesc:
        // Sort by positive balance descending (customers with most credit first)
        orderBy += 'calculated_balance DESC';
        break;
      case CustomerSortOption.dueDesc:
        // Sort by negative balance ascending (customers who owe most first)
        orderBy += 'calculated_balance ASC';
        break;
      case CustomerSortOption.nameAsc:
      case null: // Default case
        orderBy += 'c.name ASC';
    }
    baseQuery += orderBy;

    // --- 4. Add Pagination ---
    baseQuery += ' LIMIT $pageSize OFFSET $offset';

    final customerMaps = await db.rawQuery(baseQuery, whereArgs);

    final customers = customerMaps.map((map) {
      return CustomerViewModel(
        customer: Customer.fromMap(map),
        balance: (map['calculated_balance'] as double?) ?? 0.0,
        creditAmount: (map['credit_amount'] as double?) ?? 0.0,
        outstandingAmount: (map['outstanding_amount'] as double?) ?? 0.0,
      );
    }).toList();

    // --- 5. Get Summary in a separate, efficient query ---
    // Build summary query without pagination
    String summaryQuery = '''
      SELECT
        COUNT(*) as totalCount,
        SUM(CASE WHEN calculated_balance > 0 THEN calculated_balance ELSE 0 END) as totalCredit,
        SUM(CASE WHEN calculated_balance < 0 THEN ABS(calculated_balance) ELSE 0 END) as totalDue,
        SUM(CASE WHEN calculated_balance != 0 THEN 1 ELSE 0 END) as activeCount
      FROM (
        SELECT
          c.id,
          (COALESCE(credits.credit_amount, 0.0) - COALESCE(bills.outstanding_amount, 0.0)) as calculated_balance
        FROM customers c
        LEFT JOIN (
          SELECT
            customerId,
            amount as credit_amount
          FROM customer_credits
        ) credits ON c.id = credits.customerId
        LEFT JOIN (
          SELECT
            customerId,
            SUM(amount - COALESCE(partialAmount, 0.0)) as outstanding_amount
          FROM bills
          WHERE isPaid = 0
          GROUP BY customerId
        ) bills ON c.id = bills.customerId
    ''';

    // Apply the same WHERE conditions for summary (but without pagination)
    if (whereConditions.isNotEmpty) {
      summaryQuery += ' WHERE ${whereConditions.join(' AND ')}';
    }
    summaryQuery += ' ) as summary_data';

    final summaryMaps = await db.rawQuery(summaryQuery, whereArgs);

    return {
      'customers': customers,
      'summary': summaryMaps.isNotEmpty ? summaryMaps.first : {
        'totalCount': 0,
        'totalCredit': 0.0,
        'totalDue': 0.0,
        'activeCount': 0,
      },
    };
  }
}
