import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import '../models/payment.dart';
import '../models/customer.dart';
import '../services/database_service.dart';
import '../services/payment_service.dart';
import '../services/data_change_notifier_service.dart';

// Sort options for payments
enum PaymentSortOption {
  dateNewest(label: 'Date (Newest first)', icon: Icons.arrow_downward),
  dateOldest(label: 'Date (Oldest first)', icon: Icons.arrow_upward),
  amountHighest(label: 'Amount (Highest first)', icon: Icons.arrow_downward),
  amountLowest(label: 'Amount (Lowest first)', icon: Icons.arrow_upward),
  methodAZ(label: 'Payment Method (A-Z)', icon: Icons.arrow_downward),
  methodZA(label: 'Payment Method (Z-A)', icon: Icons.arrow_upward);

  final String label;
  final IconData icon;

  const PaymentSortOption({
    required this.label,
    required this.icon,
  });
}

class PaymentsController extends ChangeNotifier {
  // Data state
  List<Payment> _displayedPayments = [];
  bool _isLoading = false;
  bool _hasMoreItems = true;
  Map<int, Customer> _customersMap = {};
  
  // Customer credit state
  double _customerCredit = 0.0;
  bool _isLoadingCredit = false;

  // Multi-selection state
  bool _isSelectionMode = false;
  final Set<int> _selectedPaymentIds = {};

  // Search state
  String _searchQuery = '';

  // Pagination variables
  static const int _pageSize = 20;
  int _currentOffset = 0;

  // Sort state
  PaymentSortOption _currentSortOption = PaymentSortOption.dateNewest;
  bool _isSortActive = false;

  // Selected customer (if viewing payments for specific customer)
  Customer? _selectedCustomer;

  // Debounce timer for search
  Timer? _debounceTimer;
  static const Duration _debounceDuration = Duration(milliseconds: 300);

  // Data change subscription
  StreamSubscription? _dataChangeSubscription;
  StreamSubscription<Currency>? _currencySubscription;

  // Getters
  List<Payment> get displayedPayments => _displayedPayments;
  bool get isLoading => _isLoading;
  bool get hasMoreItems => _hasMoreItems;
  Map<int, Customer> get customersMap => _customersMap;
  double get customerCredit => _customerCredit;
  bool get isLoadingCredit => _isLoadingCredit;
  bool get isSelectionMode => _isSelectionMode;
  Set<int> get selectedPaymentIds => _selectedPaymentIds;
  String get searchQuery => _searchQuery;
  PaymentSortOption get currentSortOption => _currentSortOption;
  bool get isSortActive => _isSortActive;
  Customer? get selectedCustomer => _selectedCustomer;

  // Derived getters for summary
  double get totalAmount {
    double total = 0;
    for (var p in _displayedPayments) {
      total += p.amount;
    }
    return total;
  }

  double get billPayments {
    double total = 0;
    for (var p in _displayedPayments) {
      if (p.billId > 0) {
        total += p.amount;
      }
    }
    return total;
  }

  double get creditPayments {
    double total = 0;
    for (var p in _displayedPayments) {
      if (p.billId == 0) {
        total += p.amount;
      }
    }
    return total;
  }

  int get paymentCount => _displayedPayments.length;

  PaymentsController({Customer? selectedCustomer}) {
    _selectedCustomer = selectedCustomer;
    _initializeController();
  }

  void _initializeController() {
    // Subscribe to data changes
    _dataChangeSubscription = DataChangeNotifierService().onDataChanged.listen((changeType) {
      if (changeType == 'payment' || changeType == 'customer') {
        refresh();
      }
    });

    // Load initial data
    loadPayments();
    loadCustomersMap();
    if (_selectedCustomer != null) {
      loadCustomerCredit();
    }

    // Listen for currency changes
    _currencySubscription = CurrencyService.onCurrencyChanged.listen((_) {
      // When currency changes, refresh the UI
      notifyListeners();
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _dataChangeSubscription?.cancel();
    _currencySubscription?.cancel();
    super.dispose();
  }

  // Data loading methods
  Future<void> loadCustomersMap() async {
    try {
      // Load all customers to display their names
      final customers = await DatabaseService.getAllCustomers();

      // Create a map of customer IDs to customer names for easy lookup
      _customersMap = {for (var c in customers) c.id: c};
      notifyListeners();
    } catch (e) {
      // Debug: Error loading customers map: $e
    }
  }

  Future<void> loadPayments() async {
    if (_isLoading) return;

    _isLoading = true;
    notifyListeners();

    try {
      // Reset current offset and displayed payments
      _currentOffset = 0;

      List<Payment> payments;
      // Use the updated methods with pagination and search
      if (_selectedCustomer != null) {
        payments = await DatabaseService.getPaymentsByCustomer(
          _selectedCustomer!.id,
          offset: _currentOffset,
          limit: _pageSize,
          searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
        );
      } else {
        payments = await DatabaseService.getAllPayments(
          offset: _currentOffset,
          limit: _pageSize,
          searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
        );
      }

      // Apply sorting
      _sortPayments(payments);

      _displayedPayments = payments;
      _currentOffset = payments.length;
      _hasMoreItems = payments.length >= _pageSize;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      // Debug: Error loading payments: $e
    }
  }

  Future<void> loadMoreItems() async {
    if (_isLoading || !_hasMoreItems) return;

    _isLoading = true;
    notifyListeners();

    try {
      List<Payment> morePayments;
      if (_selectedCustomer != null) {
        morePayments = await DatabaseService.getPaymentsByCustomer(
          _selectedCustomer!.id,
          offset: _currentOffset,
          limit: _pageSize,
          searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
        );
      } else {
        morePayments = await DatabaseService.getAllPayments(
          offset: _currentOffset,
          limit: _pageSize,
          searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
        );
      }

      _displayedPayments.addAll(morePayments);
      // Apply sorting to the complete list
      _sortPayments(_displayedPayments);
      _currentOffset += morePayments.length;
      _hasMoreItems = morePayments.length >= _pageSize;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      // Debug: Error loading more payments: $e
    }
  }

  Future<void> refresh() async {
    // Reload payments and customer credit (if applicable)
    await loadPayments();
    if (_selectedCustomer != null) {
      await loadCustomerCredit();
    }
  }

  Future<void> loadCustomerCredit() async {
    if (_selectedCustomer == null) return;

    _isLoadingCredit = true;
    notifyListeners();

    try {
      // Debug: Loading credit for customer ${_selectedCustomer!.id}
      final creditAmount = await PaymentService.getCustomerCreditBalance(
          _selectedCustomer!.id);
      // Debug: Credit balance loaded: $creditAmount

      _customerCredit = creditAmount;
      _isLoadingCredit = false;
      notifyListeners();
    } catch (e) {
      _isLoadingCredit = false;
      notifyListeners();
      // Debug: Error loading customer credit: $e
    }
  }

  Future<void> resetCustomerCredit() async {
    if (_selectedCustomer == null) return;

    _isLoadingCredit = true;
    notifyListeners();

    try {
      await PaymentService.resetCustomerCredit(_selectedCustomer!.id);
      _customerCredit = 0;
    } catch (e) {
      debugPrint('Error resetting customer credit: $e');
      rethrow;
    } finally {
      _isLoadingCredit = false;
      notifyListeners();
    }
  }

  // Search functionality
  void onSearchChanged(String query) {
    if (_debounceTimer?.isActive ?? false) _debounceTimer?.cancel();

    _debounceTimer = Timer(_debounceDuration, () {
      if (_searchQuery != query.toLowerCase()) {
        _searchQuery = query.toLowerCase();
        // Reset and reload
        loadPayments();
      }
    });
  }

  void clearSearch() {
    _searchQuery = '';
    loadPayments();
  }

  // Sort functionality
  void _sortPayments(List<Payment> payments) {
    switch (_currentSortOption) {
      case PaymentSortOption.dateNewest:
        payments.sort((a, b) => b.paymentDate.compareTo(a.paymentDate));
        break;
      case PaymentSortOption.dateOldest:
        payments.sort((a, b) => a.paymentDate.compareTo(b.paymentDate));
        break;
      case PaymentSortOption.amountHighest:
        payments.sort((a, b) => b.amount.compareTo(a.amount));
        break;
      case PaymentSortOption.amountLowest:
        payments.sort((a, b) => a.amount.compareTo(b.amount));
        break;
      case PaymentSortOption.methodAZ:
        payments.sort((a, b) {
          final methodA = a.paymentMethod ?? '';
          final methodB = b.paymentMethod ?? '';
          final comparison = methodA.compareTo(methodB);
          return comparison != 0 ? comparison : b.paymentDate.compareTo(a.paymentDate);
        });
        break;
      case PaymentSortOption.methodZA:
        payments.sort((a, b) {
          final methodA = a.paymentMethod ?? '';
          final methodB = b.paymentMethod ?? '';
          final comparison = methodB.compareTo(methodA);
          return comparison != 0 ? comparison : b.paymentDate.compareTo(a.paymentDate);
        });
        break;
    }
  }

  // Handle sort option change
  void onSortChanged(PaymentSortOption option) {
    _currentSortOption = option;
    _isSortActive = option != PaymentSortOption.dateNewest;

    // Re-sort current payments
    _sortPayments(_displayedPayments);
    notifyListeners();
  }

  // Selection mode functionality
  void enterSelectionMode(int paymentId) {
    _isSelectionMode = true;
    _selectedPaymentIds.add(paymentId);
    notifyListeners();
  }

  void exitSelectionMode() {
    _isSelectionMode = false;
    _selectedPaymentIds.clear();
    notifyListeners();
  }

  void togglePaymentSelection(int paymentId) {
    if (_selectedPaymentIds.contains(paymentId)) {
      _selectedPaymentIds.remove(paymentId);
    } else {
      _selectedPaymentIds.add(paymentId);
    }
    
    // Exit selection mode if no items are selected
    if (_selectedPaymentIds.isEmpty) {
      _isSelectionMode = false;
    }
    
    notifyListeners();
  }

  // Delete functionality
  Future<void> deletePayment(int paymentId) async {
    try {
      await DatabaseService.deletePayment(paymentId);
      // Remove from displayed list
      _displayedPayments.removeWhere((payment) => payment.id == paymentId);
      notifyListeners();
    } catch (e) {
      // Debug: Error deleting payment: $e
      rethrow;
    }
  }

  Future<void> deleteSelectedPayments() async {
    try {
      for (final paymentId in _selectedPaymentIds) {
        await DatabaseService.deletePayment(paymentId);
      }
      
      // Remove from displayed list
      _displayedPayments.removeWhere((payment) => _selectedPaymentIds.contains(payment.id));
      
      // Exit selection mode
      exitSelectionMode();
      
      notifyListeners();
    } catch (e) {
      // Debug: Error deleting selected payments: $e
      rethrow;
    }
  }
}
