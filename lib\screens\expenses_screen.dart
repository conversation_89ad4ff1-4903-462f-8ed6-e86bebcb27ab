import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:tubewell_water_billing/controllers/expenses_controller.dart';
import 'package:tubewell_water_billing/models/expense.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing/widgets/app_drawer.dart';
import 'package:tubewell_water_billing/widgets/empty_state_widget.dart';
import 'package:tubewell_water_billing/forms/expense_form_screen.dart';
import 'package:tubewell_water_billing/widgets/universal_fab.dart';
import 'package:tubewell_water_billing/utils/navigation_helper.dart';
import 'package:tubewell_water_billing/services/pdf_services/universal_pdf_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_settings.dart';

class ExpensesScreen extends StatelessWidget {
  const ExpensesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ExpensesController(),
      child: const _ExpensesView(),
    );
  }
}

class _ExpensesView extends StatefulWidget {
  const _ExpensesView();

  @override
  State<_ExpensesView> createState() => _ExpensesViewState();
}

class _ExpensesViewState extends State<_ExpensesView> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  final Map<String, IconData> _categoryIcons = {
    'Electricity': Icons.electric_bolt,
    'Maintenance': Icons.handyman,
    'Repairs': Icons.build,
    'Fuel': Icons.local_gas_station,
    'Salaries': Icons.payments,
    'Equipment': Icons.construction,
    'Rent': Icons.home,
    'Taxes': Icons.receipt_long,
    'Transportation': Icons.directions_car,
    'Office Supplies': Icons.business_center,
    'Other': Icons.category,
  };

  @override
  void initState() {
    super.initState();
    final controller = Provider.of<ExpensesController>(context, listen: false);
    _scrollController.addListener(() => _scrollListener(controller));
    _searchController.addListener(() => _onSearchChanged(controller));
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onSearchChanged(ExpensesController controller) {
    controller.onSearchChanged(_searchController.text);
  }

  void _scrollListener(ExpensesController controller) {
    if (_scrollController.position.pixels >
        _scrollController.position.maxScrollExtent - 200) {
      controller.loadMoreExpenses();
    }
  }

  void _clearSearch(ExpensesController controller) {
    _searchController.clear();
    controller.clearSearch();
  }

  Future<void> _deleteExpense(BuildContext context, ExpensesController controller, Expense expense) async {
    final deleted = await controller.deleteExpense(expense.id);
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(deleted ? 'Expense deleted successfully' : 'Failed to delete expense'),
          backgroundColor: deleted ? Colors.green : Colors.red,
        ),
      );
    }
  }

  void _showDeleteConfirmation(BuildContext context, ExpensesController controller, Expense expense) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Delete Expense'),
        content: Text('Are you sure you want to delete this expense?\n\n'
            'Amount: ${CurrencyService.formatCurrency(expense.amount)}\n'
            'Category: ${expense.category}'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              _deleteExpense(context, controller, expense);
            },
            child: const Text('DELETE', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Future<void> _navigateToExpenseForm(BuildContext context, {Expense? existingExpense}) async {
    final controller = Provider.of<ExpensesController>(context, listen: false);
    await NavigationHelper.navigateTo(
      context,
      ExpenseFormScreen(
        existingExpense: existingExpense,
      ),
    );
    controller.refreshExpenses();
  }

  void _showSortDialog(BuildContext context, ExpensesController controller) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Sort Expenses'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: ExpenseSortOption.values.map((option) {
            final isSelected = option == controller.currentSortOption;
            return ListTile(
              leading: Icon(
                option.icon,
                color: isSelected ? Colors.purple : null,
              ),
              title: Text(option.label),
              trailing: isSelected
                  ? const Icon(Icons.check_circle, color: Colors.purple)
                  : null,
              selected: isSelected,
              selectedColor: Colors.purple,
              onTap: () {
                Navigator.pop(dialogContext);
                controller.onSortChanged(option);
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildExpenseItem(BuildContext context, ExpensesController controller, Expense expense) {
    final categoryIcon = _categoryIcons[expense.category] ?? Icons.category;
    final formattedDate = DateFormat('dd MMM yyyy').format(expense.date);
    final formattedAmount = CurrencyService.formatCurrency(expense.amount);
    final isSelected = controller.selectedExpenseIds.contains(expense.id);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: isSelected ? Colors.amber.shade500 : Colors.indigo.shade400,
            width: isSelected ? 2.0 : 1.5,
          ),
        ),
        color: isSelected ? Colors.amber.shade50 : null,
        child: InkWell(
          onTap: controller.isSelectionMode
              ? () {
                  controller.toggleSelection(expense.id);
                  HapticFeedback.selectionClick();
                }
              : () => _navigateToExpenseForm(context, existingExpense: expense),
          onLongPress: controller.isSelectionMode
              ? null
              : () {
                  controller.toggleSelection(expense.id);
                  HapticFeedback.heavyImpact();
                },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Row 1: Category and Payment Method
                Row(
                  children: [
                    // Selection checkbox when in selection mode
                    if (controller.isSelectionMode) ...[
                      Checkbox(
                        value: isSelected,
                        onChanged: (_) {
                          controller.toggleSelection(expense.id);
                          HapticFeedback.selectionClick();
                        },
                        activeColor: Colors.amber.shade700,
                        checkColor: Colors.white,
                      ),
                      const SizedBox(width: 8),
                    ],

                    // Category with icon
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 5),
                        decoration: BoxDecoration(
                          color: Colors.teal.shade50,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: Colors.teal.shade200),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              categoryIcon,
                              color: Colors.teal.shade700,
                              size: 14,
                            ),
                            const SizedBox(width: 3),
                            Expanded(
                              child: Text(
                                expense.category,
                                style: TextStyle(
                                  color: Colors.teal.shade800,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 6),

                    // Payment Method with icon
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 5),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(_getPaymentMethodIcon(expense.paymentMethod),
                                size: 14, color: Colors.blue.shade700),
                            const SizedBox(width: 3),
                            Expanded(
                              child: Text(
                                expense.paymentMethod ?? 'Cash',
                                style: TextStyle(
                                  color: Colors.blue.shade800,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Edit button in row 1 (only when not in selection mode)
                    if (!controller.isSelectionMode)
                      GestureDetector(
                        onTap: () =>
                            _navigateToExpenseForm(context, existingExpense: expense),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.cyan.shade50,
                            borderRadius: BorderRadius.circular(5),
                            border: Border.all(color: Colors.cyan.shade200),
                          ),
                          child: Icon(Icons.edit,
                              color: Colors.cyan.shade700, size: 12),
                        ),
                      ),
                  ],
                ),

                const SizedBox(height: 12),

                // Row 2: Date and Amount
                Row(
                  children: [
                    // Date with icon
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 5),
                        decoration: BoxDecoration(
                          color: Colors.purple.shade50,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: Colors.purple.shade200),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.calendar_today,
                                size: 14, color: Colors.purple.shade700),
                            const SizedBox(width: 3),
                            Expanded(
                              child: Text(
                                formattedDate,
                                style: TextStyle(
                                  color: Colors.purple.shade800,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 6),

                    // Amount with icon
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 5),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.payments,
                                size: 14, color: Colors.red.shade700),
                            const SizedBox(width: 3),
                            Expanded(
                              child: Text(
                                formattedAmount,
                                style: TextStyle(
                                  color: Colors.red.shade800,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Delete button
                    if (!controller.isSelectionMode)
                      GestureDetector(
                        onTap: () => _showDeleteConfirmation(context, controller, expense),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.red.shade50,
                            borderRadius: BorderRadius.circular(5),
                            border: Border.all(color: Colors.red.shade200),
                          ),
                          child: Icon(Icons.delete,
                              color: Colors.red.shade700, size: 12),
                        ),
                      ),
                  ],
                ),

                // Row 3: Remarks (if available)
                if (expense.remarks != null && expense.remarks!.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                    decoration: BoxDecoration(
                      color: Colors.amber.shade50,
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(color: Colors.amber.shade200),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.comment,
                            size: 14, color: Colors.amber.shade700),
                        const SizedBox(width: 3),
                        Expanded(
                          child: Text(
                            expense.remarks!,
                            style: TextStyle(
                              color: Colors.amber.shade800,
                              fontSize: 12,
                              fontStyle: FontStyle.italic,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildScrollableContent(BuildContext context, ExpensesController controller, Widget content) {
    if (controller.isLoading && controller.expenses.isEmpty) {
      return content;
    } else if (controller.expenses.isEmpty) {
      return content;
    } else {
      return CustomScrollView(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          SliverToBoxAdapter(
            child: _buildExpenseSummaryCard(context, controller),
          ),
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                if (index == controller.expenses.length) {
                  return controller.isLoadingMore
                      ? const Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Center(child: CircularProgressIndicator()),
                        )
                      : const SizedBox.shrink();
                }
                final expense = controller.expenses[index];
                return _buildExpenseItem(context, controller, expense);
              },
              childCount: controller.expenses.length + (controller.hasMoreData ? 1 : 0),
            ),
          ),
          const SliverToBoxAdapter(
            child: SizedBox(height: 80),
          ),
        ],
      );
    }
  }

  Widget _buildExpensesList(BuildContext context, ExpensesController controller) {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.only(bottom: 80),
      physics: const AlwaysScrollableScrollPhysics(),
      itemCount: controller.expenses.length + (controller.hasMoreData ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == controller.expenses.length) {
          return controller.isLoadingMore
              ? const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Center(child: CircularProgressIndicator()),
                )
              : const SizedBox.shrink();
        }
        final expense = controller.expenses[index];
        return _buildExpenseItem(context, controller, expense);
      },
    );
  }

  Widget _buildExpenseSummaryCard(BuildContext context, ExpensesController controller) {
    List<MapEntry<String, double>> topCategories = [];
    if (controller.categoryTotals.isNotEmpty) {
      topCategories = controller.categoryTotals.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));
      if (topCategories.length > 3) {
        topCategories = topCategories.sublist(0, 3);
      }
    }

    final int expenseCount = controller.expenseSummary['count']?.toInt() ?? 0;
    final double totalAmount = controller.expenseSummary['totalAmount']?.toDouble() ?? 0.0;

    // Calculate average expense
    final double averageAmount =
        expenseCount > 0 ? totalAmount / expenseCount : 0.0;

    return Container(
      margin: const EdgeInsets.fromLTRB(8, 8, 8, 0),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.indigo.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.indigo.shade200),
      ),
      child: Column(
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Expenses Summary',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.indigo.shade900,
                  fontSize: 18,
                ),
              ),
              Text(
                '$expenseCount expenses',
                style: TextStyle(
                  color: Colors.indigo.shade800,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Main amount with highlight
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.indigo.shade100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.indigo.shade300),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.money_off,
                      color: Colors.indigo.shade700,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Total Expenses',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 15,
                        color: Colors.indigo.shade800,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                controller.isLoading
                    ? const SizedBox(
                        height: 24,
                        width: 24,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          CurrencyService.formatCurrency(totalAmount),
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 22,
                            color: Colors.indigo.shade700,
                          ),
                        ),
                      ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Detailed stats
          Row(
            children: [
              // Average expense
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Average',
                  value: CurrencyService.formatCurrency(averageAmount),
                  icon: Icons.calculate,
                  iconColor: Colors.purple.shade700,
                  bgColor: Colors.purple.shade50,
                  borderColor: Colors.purple.shade200,
                  textColor: Colors.purple.shade800,
                ),
              ),
              const SizedBox(width: 16),
              // Top category if available
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: topCategories.isNotEmpty
                      ? 'Top Category'
                      : 'No Categories',
                  value:
                      topCategories.isNotEmpty ? topCategories.first.key : '-',
                  icon: topCategories.isNotEmpty
                      ? _categoryIcons[topCategories.first.key] ??
                          Icons.category
                      : Icons.category,
                  iconColor: Colors.teal.shade700,
                  bgColor: Colors.teal.shade50,
                  borderColor: Colors.teal.shade200,
                  textColor: Colors.teal.shade800,
                ),
              ),
            ],
          ),


        ],
      ),
    );
  }

  // Helper method to build a financial summary item
  Widget _buildFinancialSummaryItem({
    required String title,
    required String value,
    required IconData icon,
    required Color iconColor,
    required Color bgColor,
    required Color borderColor,
    required Color textColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: iconColor, size: 16),
              const SizedBox(width: 6),
              Flexible(
                child: Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                    color: textColor,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: textColor,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to get payment method icon
  IconData _getPaymentMethodIcon(String? method) {
    if (method == null) return Icons.money;

    switch (method.toLowerCase()) {
      case 'cash':
        return Icons.money;
      case 'bank transfer':
        return Icons.account_balance;
      case 'check':
        return Icons.receipt_long;
      case 'credit card':
        return Icons.credit_card;
      default:
        return Icons.payment;
    }
  }

  void _deleteSelectedExpenses(BuildContext context, ExpensesController controller) async {
    if (controller.selectedExpenseIds.isEmpty) return;

    final count = controller.selectedExpensesCount;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Delete Expenses'),
        content: Text('Are you sure you want to delete $count selected expenses?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(true),
            child: const Text('DELETE', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed != true || !mounted) return;

    final successCount = await controller.deleteSelectedExpenses();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Deleted $successCount of $count expenses'),
          backgroundColor: successCount > 0 ? Colors.green : Colors.orange,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ExpensesController>(
      builder: (context, controller, child) {
        Widget content;

        if (controller.isLoading && controller.expenses.isEmpty) {
          content = const Center(child: CircularProgressIndicator());
        } else if (controller.expenses.isEmpty) {
          content = EmptyStateWidget(
            icon: controller.searchQuery.isNotEmpty ? Icons.search_off : Icons.receipt_long,
            title: controller.searchQuery.isNotEmpty ? 'No Results Found' : 'No Expenses Yet',
            message: controller.searchQuery.isNotEmpty ? 'Try different search terms' : 'Add your first expense to get started.',
            buttonText: controller.searchQuery.isNotEmpty ? 'Clear Search' : 'Add Expense',
            onButtonPressed: () {
              if (controller.searchQuery.isNotEmpty) {
                _clearSearch(controller);
              } else {
                _navigateToExpenseForm(context);
              }
            },
          );
        } else {
          content = _buildExpensesList(context, controller);
        }

        return Scaffold(
          appBar: controller.isSelectionMode
              ? AppBar(
                  backgroundColor: const Color(0xFF2E7D32),
                  title: Text('${controller.selectedExpensesCount} selected'),
                  leading: IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => controller.clearSelection(),
                  ),
                  actions: [
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.white),
                      onPressed: controller.selectedExpenseIds.isEmpty
                          ? null
                          : () => _deleteSelectedExpenses(context, controller),
                    ),
                  ],
                )
              : TubewellAppBar(
                  title: 'Expenses',
                  currentScreen: 'expenses',
                  showBackButton: false,
                  onPdfPressed: () => controller.generateExpensesPdf(context),
                  pdfData: {
                    'expenses': controller.expenses,
                    'summary': controller.expenseSummary,
                    'categoryTotals': controller.categoryTotals,
                    'searchQuery': controller.searchQuery.isNotEmpty ? controller.searchQuery : null,
                    'sortOption': controller.currentSortOption.label,
                  },
                ),
          drawer: const AppDrawer(currentScreen: 'expenses'),
          body: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _searchController,
                            decoration: InputDecoration(
                              hintText: 'Search expenses...',
                              prefixIcon: const Icon(Icons.search),
                              suffixIcon: _searchController.text.isNotEmpty
                                  ? IconButton(
                                      icon: const Icon(Icons.clear),
                                      onPressed: () => _clearSearch(controller),
                                    )
                                  : null,
                              contentPadding: const EdgeInsets.symmetric(vertical: 10),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(color: Colors.grey.shade300),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(color: Colors.grey.shade300),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(color: Colors.indigo.shade300),
                              ),
                              filled: true,
                              fillColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: IconButton(
                            icon: Stack(
                              alignment: Alignment.center,
                              children: [
                                const Icon(Icons.sort),
                                if (controller.isSortActive)
                                  Positioned(
                                    right: 0,
                                    bottom: 0,
                                    child: Container(
                                      width: 8,
                                      height: 8,
                                      decoration: const BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: Colors.purple,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                            tooltip: 'Sort: ${controller.currentSortOption.label}',
                            onPressed: () => _showSortDialog(context, controller),
                          ),
                        ),
                      ],
                    ),
                    if (controller.searchQuery.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Text(
                            'Found ${controller.expenses.length} results',
                            style: TextStyle(
                              color: Colors.indigo.shade600,
                              fontStyle: FontStyle.italic,
                              fontSize: 13,
                            ),
                          ),
                          const Spacer(),
                          TextButton(
                            onPressed: () => _clearSearch(controller),
                            style: TextButton.styleFrom(
                              foregroundColor: const Color(0xFF2E7D32),
                              padding: const EdgeInsets.symmetric(horizontal: 8),
                              minimumSize: const Size(0, 32),
                            ),
                            child: const Text('Clear Search'),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
              Expanded(
                child: RefreshIndicator(
                  onRefresh: controller.refreshExpenses,
                  color: const Color(0xFF2E7D32),
                  child: _buildScrollableContent(context, controller, content),
                ),
              ),
            ],
          ),
          floatingActionButton: controller.isSelectionMode
              ? null
              : UniversalFab(
                  type: FabType.expense,
                  heroTag: 'fab-expenses',
                  onResult: (result) {
                    if (result == true) {
                      controller.refreshExpenses();
                    }
                  },
                ),
        );
      },
    );
  }
}
