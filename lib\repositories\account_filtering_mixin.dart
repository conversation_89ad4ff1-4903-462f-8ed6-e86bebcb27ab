import 'package:tubewell_water_billing/services/account_service.dart';

/// Mixin that provides centralized account filtering logic for repositories.
/// This eliminates the need for manual duplication of account filtering code
/// across all repository methods.
mixin AccountFilteringMixin {
  /// Get current account ID from AccountService
  String? get _currentAccountId => AccountService.currentAccount?.id;

  /// Build account-aware WHERE clause and arguments for simple queries
  /// 
  /// Returns a map with 'where' and 'whereArgs' keys that can be used
  /// directly with db.query() methods.
  /// 
  /// Example usage:
  /// ```dart
  /// final filter = buildAccountFilter();
  /// final maps = await db.query('table_name', 
  ///   where: filter['where'], 
  ///   whereArgs: filter['whereArgs']
  /// );
  /// ```
  Map<String, dynamic> buildAccountFilter({String? additionalWhere, List<dynamic>? additionalArgs}) {
    String whereClause;
    List<dynamic> whereArgs = [];

    if (_currentAccountId != null) {
      whereClause = 'accountId = ? OR accountId IS NULL';
      whereArgs.add(_currentAccountId);
    } else {
      whereClause = 'accountId IS NULL';
    }

    // Add additional conditions if provided
    if (additionalWhere != null && additionalWhere.isNotEmpty) {
      whereClause = '($whereClause) AND ($additionalWhere)';
      if (additionalArgs != null) {
        whereArgs.addAll(additionalArgs);
      }
    }

    return {
      'where': whereClause,
      'whereArgs': whereArgs,
    };
  }

  /// Build account-aware WHERE clause for raw SQL queries
  /// 
  /// This method is designed for use in raw SQL queries where you need
  /// to add account filtering to an existing WHERE clause.
  /// 
  /// [tableAlias] - Optional table alias (e.g., 'b' for 'bills b')
  /// [existingArgs] - List to append the account arguments to
  /// 
  /// Returns the WHERE clause fragment to be added to your query.
  /// 
  /// Example usage:
  /// ```dart
  /// String query = 'SELECT * FROM bills b WHERE 1=1';
  /// List<dynamic> args = [];
  /// query += buildAccountFilterForRawQuery('b', args);
  /// final results = await db.rawQuery(query, args);
  /// ```
  String buildAccountFilterForRawQuery(String? tableAlias, List<dynamic> existingArgs) {
    final prefix = tableAlias != null ? '$tableAlias.' : '';
    
    if (_currentAccountId != null) {
      existingArgs.add(_currentAccountId);
      return ' AND (${prefix}accountId = ? OR ${prefix}accountId IS NULL)';
    } else {
      return ' AND ${prefix}accountId IS NULL';
    }
  }

  /// Build account-aware WHERE clause for complex queries with custom conditions
  /// 
  /// This is the most flexible method for building account filters when you need
  /// to combine account filtering with other complex conditions.
  /// 
  /// [customCondition] - Your custom WHERE conditions (without the account filter)
  /// [customArgs] - Arguments for your custom conditions
  /// [tableAlias] - Optional table alias
  /// 
  /// Returns a map with 'where' and 'whereArgs' keys.
  /// 
  /// Example usage:
  /// ```dart
  /// final filter = buildComplexAccountFilter(
  ///   customCondition: 'customerId = ? AND isPaid = ?',
  ///   customArgs: [customerId, false],
  ///   tableAlias: 'b'
  /// );
  /// final maps = await db.query('bills', 
  ///   where: filter['where'], 
  ///   whereArgs: filter['whereArgs']
  /// );
  /// ```
  Map<String, dynamic> buildComplexAccountFilter({
    String? customCondition,
    List<dynamic>? customArgs,
    String? tableAlias,
  }) {
    final prefix = tableAlias != null ? '$tableAlias.' : '';
    String whereClause;
    List<dynamic> whereArgs = [];

    // Build account filter
    if (_currentAccountId != null) {
      whereClause = '(${prefix}accountId = ? OR ${prefix}accountId IS NULL)';
      whereArgs.add(_currentAccountId);
    } else {
      whereClause = '${prefix}accountId IS NULL';
    }

    // Add custom conditions if provided
    if (customCondition != null && customCondition.isNotEmpty) {
      whereClause = '$whereClause AND ($customCondition)';
      if (customArgs != null) {
        whereArgs.addAll(customArgs);
      }
    }

    return {
      'where': whereClause,
      'whereArgs': whereArgs,
    };
  }
}
