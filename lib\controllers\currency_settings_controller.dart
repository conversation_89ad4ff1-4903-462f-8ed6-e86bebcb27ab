import 'package:flutter/material.dart';
import 'package:tubewell_water_billing/models/currency.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';

class CurrencySettingsController with ChangeNotifier {
  Currency _selectedCurrency = Currencies.pakistaniRupee;
  final List<Currency> _availableCurrencies = Currencies.all;
  bool _isLoading = true;
  final TextEditingController hourlyRateController = TextEditingController(text: '900');

  // Getters
  Currency get selectedCurrency => _selectedCurrency;
  List<Currency> get availableCurrencies => _availableCurrencies;
  bool get isLoading => _isLoading;

  CurrencySettingsController() {
    loadCurrencySettings();
  }

  @override
  void dispose() {
    hourlyRateController.dispose();
    super.dispose();
  }

  Future<void> loadCurrencySettings() async {
    _isLoading = true;
    notifyListeners();

    try {
      await CurrencyService.initialize();
      _selectedCurrency = CurrencyService.currentCurrency;
      final defaultHourlyRate = await CurrencyService.getDefaultHourlyRate();
      hourlyRateController.text = defaultHourlyRate.toString();
    } catch (e) {
      // Handle error
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void setSelectedCurrency(Currency currency) {
    _selectedCurrency = currency;
    notifyListeners();
  }

  Future<bool> saveSettings() async {
    _isLoading = true;
    notifyListeners();

    try {
      final hourlyRate = double.tryParse(hourlyRateController.text) ?? 900.0;
      await CurrencyService.setCurrency(_selectedCurrency);
      await CurrencyService.saveDefaultHourlyRate(hourlyRate);
      return true;
    } catch (e) {
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
