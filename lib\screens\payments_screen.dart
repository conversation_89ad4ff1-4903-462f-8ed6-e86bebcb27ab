import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:tubewell_water_billing/controllers/payments_controller.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/models/payment.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing/widgets/empty_state_widget.dart';
import 'package:tubewell_water_billing/forms/auto_payment_form_screen.dart';
import 'package:tubewell_water_billing/widgets/app_drawer.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/widgets/payment_list_item.dart';


class PaymentsScreen extends StatefulWidget {
  final Customer? selectedCustomer;

  const PaymentsScreen({
    super.key,
    this.selectedCustomer,
  });

  // Static method to show customer selection dialog for payment
  static void showCustomerSelectionForPayment(BuildContext context,
      {VoidCallback? onPaymentAdded}) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Select Customer for Payment'),
        content: FutureBuilder<List<Customer>>(
          future: DatabaseService.getAllCustomers(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return const Text('Error loading customers');
            }

            final customers = snapshot.data ?? [];

            if (customers.isEmpty) {
              return const Text(
                  'No customers found. Please add a customer first.');
            }

            return SizedBox(
              width: double.maxFinite,
              height: 300,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: customers.length,
                itemBuilder: (context, index) {
                  final customer = customers[index];
                  return ListTile(
                    title: Text(customer.name),
                    subtitle: customer.contactNumber != null
                        ? Text(customer.contactNumber!)
                        : null,
                    onTap: () {
                      Navigator.of(dialogContext).pop();
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => AutoPaymentFormScreen(
                            customer: customer,
                          ),
                        ),
                      ).then((result) {
                        // If payment was added successfully, refresh
                        if (result == true && onPaymentAdded != null) {
                          onPaymentAdded();
                        }
                      });
                    },
                  );
                },
              ),
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('CANCEL'),
          ),
        ],
      ),
    );
  }

  @override
  State<PaymentsScreen> createState() => _PaymentsScreenState();
}

class _PaymentsScreenState extends State<PaymentsScreen>
    with AutomaticKeepAliveClientMixin {
  // Keep state alive only when we have payment data
  @override
  bool get wantKeepAlive => true;

  // Search controller
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }





  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return ChangeNotifierProvider(
      create: (context) => PaymentsController(selectedCustomer: widget.selectedCustomer),
      child: Consumer<PaymentsController>(
        builder: (context, controller, child) {
          return Scaffold(
      appBar: controller.isSelectionMode
          ? AppBar(
              backgroundColor: const Color(0xFF2E7D32),
              title: Text('${controller.selectedPaymentIds.length} selected'),
              leading: IconButton(
                icon: const Icon(Icons.close, color: Colors.white),
                onPressed: () {
                  controller.exitSelectionMode();
                },
              ),
              actions: [
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.white),
                  onPressed: controller.selectedPaymentIds.isEmpty
                      ? null
                      : () => controller.deleteSelectedPayments(),
                ),
              ],
            )
          : TubewellAppBar(
              title: widget.selectedCustomer != null
                  ? '${widget.selectedCustomer!.name} Payments'
                  : 'Payments',
              currentScreen: 'payments',
              showBackButton: false,
              pdfData: {
                'payments': controller.displayedPayments,
                'customer': widget.selectedCustomer,
                'showPayments': true,
              },
            ),
      drawer: const AppDrawer(currentScreen: 'payments'),
      body: _buildContent(controller),
      floatingActionButton: controller.isSelectionMode
          ? null
          : FloatingActionButton(
              heroTag: 'fab-payments',
              backgroundColor: const Color(0xFF2E7D32),
              onPressed: () {
                PaymentsScreen.showCustomerSelectionForPayment(
                  context,
                  onPaymentAdded: controller.refresh,
                );
              },
              child: const Icon(Icons.add, color: Colors.white),
            ),
    );
  }

  Widget _buildContent(PaymentsController controller) {
    return Column(
      children: [
        // Search bar - keep fixed at top
        _buildSearchBar(controller),

        Expanded(
          child: controller.isLoading && controller.displayedPayments.isEmpty
              ? const Center(child: CircularProgressIndicator())
              : controller.displayedPayments.isEmpty
                  ? EmptyStateWidget(
                      icon: Icons.payment,
                      title: 'No Payments Yet',
                      message: 'No payments have been recorded yet.',
                      buttonText: 'Record Payment',
                      onButtonPressed: () {
                        PaymentsScreen.showCustomerSelectionForPayment(context);
                      },
                    )
                  : _buildScrollableContent(controller),
        ),
      ],
    );
  }

  Widget _buildPaymentSummary(PaymentsController controller) {
    return Container(
      margin: const EdgeInsets.fromLTRB(8, 8, 8, 0),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.purple.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.purple.shade200),
      ),
      child: Column(
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Payment Summary',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.purple.shade900,
                  fontSize: 18,
                ),
              ),
              Text(
                '${controller.paymentCount} payments',
                style: TextStyle(
                  color: Colors.purple.shade800,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Main amount with highlight
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.purple.shade100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.purple.shade300),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.account_balance_wallet,
                      color: Colors.purple.shade700,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Total Payments',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.purple.shade800,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      CurrencyService.formatCurrency(controller.totalAmount),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 24,
                        color: Colors.purple.shade700,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Detailed stats
          Row(
            children: [
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Bill Payments',
                  value: CurrencyService.formatCurrency(controller.billPayments),
                  icon: Icons.receipt,
                  iconColor: Colors.purple.shade700,
                  bgColor: Colors.purple.shade50,
                  borderColor: Colors.purple.shade200,
                  textColor: Colors.purple.shade800,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Credit',
                  value: CurrencyService.formatCurrency(controller.creditPayments),
                  icon: Icons.savings,
                  iconColor: Colors.indigo.shade700,
                  bgColor: Colors.indigo.shade50,
                  borderColor: Colors.indigo.shade200,
                  textColor: Colors.indigo.shade800,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialSummaryItem({
    required String title,
    required String value,
    required IconData icon,
    required Color iconColor,
    required Color bgColor,
    required Color textColor,
    required Color borderColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: iconColor, size: 16),
              const SizedBox(width: 6),
              Flexible(
                child: Text(
                  title,
                  style: TextStyle(
                    color: textColor,
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: textColor,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(PaymentsController controller) {
    // Set up search controller listener
    _searchController.removeListener(() {});
    _searchController.addListener(() {
      controller.onSearchChanged(_searchController.text);
    });

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search payments...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              controller.onSearchChanged('');
                            },
                          )
                        : null,
                    contentPadding: const EdgeInsets.symmetric(vertical: 10),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide:
                          BorderSide(color: Colors.purple.shade400, width: 2),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // Sort button
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: IconButton(
                  icon: Stack(
                    alignment: Alignment.center,
                    children: [
                      const Icon(Icons.sort),
                      if (controller.isSortActive)
                        Positioned(
                          right: 0,
                          bottom: 0,
                          child: Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.purple,
                            ),
                          ),
                        ),
                    ],
                  ),
                  tooltip: 'Sort: ${controller.currentSortOption.label}',
                  onPressed: () => _showSortDialog(controller),
                ),
              ),
            ],
          ),

          // Search results indicator
          if (_searchController.text.isNotEmpty)
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 4.0, vertical: 8.0),
              child: Row(
                children: [
                  Text(
                    'Found ${controller.displayedPayments.length} results',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () {
                      _searchController.clear();
                      controller.onSearchChanged('');
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: const Color(0xFF2E7D32),
                    ),
                    child: const Text('Clear Search'),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  // Show sort options dialog
  void _showSortDialog(PaymentsController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sort Payments'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: PaymentSortOption.values.map((option) {
            final isSelected = option == controller.currentSortOption;
            return ListTile(
              leading: Icon(
                option.icon,
                color: isSelected ? Colors.purple : null,
              ),
              title: Text(option.label),
              trailing: isSelected
                  ? const Icon(Icons.check_circle, color: Colors.purple)
                  : null,
              selected: isSelected,
              selectedColor: Colors.purple,
              onTap: () {
                Navigator.pop(context);
                controller.onSortChanged(option);
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildScrollableContent(PaymentsController controller) {
    // Set up scroll controller listener
    _scrollController.removeListener(() {});
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent - 500 &&
          !controller.isLoading &&
          controller.hasMoreItems) {
        controller.loadMoreItems();
      }
    });

    return RefreshIndicator(
      onRefresh: () => controller.refresh(),
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // Customer credit info if a specific customer is selected
          if (widget.selectedCustomer != null)
            SliverToBoxAdapter(
              child: _buildCustomerCreditInfo(controller),
            ),

          // Payment summary section
          SliverToBoxAdapter(
            child: _buildPaymentSummary(controller),
          ),

          // Payment list
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                if (index >= controller.displayedPayments.length) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(8.0),
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                final payment = controller.displayedPayments[index];
                final customer = controller.customersMap[payment.customerId];

                return PaymentListItem(
                  payment: payment,
                  customer: customer,
                  onDelete: (payment) => controller.deletePayment(payment.id),
                  onPaymentUpdated: () => controller.refresh(),
                  isSelectionMode: controller.isSelectionMode,
                  isSelected: controller.selectedPaymentIds.contains(payment.id),
                  onToggleSelection: () => controller.togglePaymentSelection(payment.id),
                  onLongPress: () => controller.enterSelectionMode(payment.id),
                );
              },
              childCount: controller.displayedPayments.length + (controller.hasMoreItems ? 1 : 0),
            ),
          ),
        ],
      ),
    );
        },
      ),
    );
  }

  // Show sort options dialog
  void _showSortDialog(PaymentsController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sort Payments'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: PaymentSortOption.values.map((option) {
            final isSelected = option == controller.currentSortOption;
            return ListTile(
              leading: Icon(
                option.icon,
                color: isSelected ? Colors.purple : null,
              ),
              title: Text(option.label),
              trailing: isSelected
                  ? const Icon(Icons.check_circle, color: Colors.purple)
                  : null,
              selected: isSelected,
              selectedColor: Colors.purple,
              onTap: () {
                Navigator.pop(context);
                controller.onSortChanged(option);
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerCreditInfo(PaymentsController controller) {
    return Container(
      margin: const EdgeInsets.fromLTRB(8, 8, 8, 0),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.purple.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.purple.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with customer name and action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(Icons.person, color: Colors.purple.shade700),
                  const SizedBox(width: 8),
                  Text(
                    widget.selectedCustomer!.name,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.purple.shade900,
                      fontSize: 18,
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  // Refresh button
                  GestureDetector(
                    onTap: () => controller.loadCustomerCredit(),
                    child: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Icon(Icons.refresh,
                          size: 16, color: Colors.blue.shade700),
                    ),
                  ),
                  const SizedBox(width: 8),
                  if (controller.customerCredit > 0)
                    GestureDetector(
                      onTap: () => _showResetCreditDialog(context, controller),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.refresh,
                                size: 14, color: Colors.red.shade700),
                            const SizedBox(width: 4),
                            Text(
                              'Reset Credit',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.red.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Credit amount display
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.purple.shade100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.purple.shade300),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.account_balance_wallet,
                      color: Colors.purple.shade700,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Available Credit',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.purple.shade800,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                controller.isLoadingCredit
                    ? const SizedBox(
                        height: 24,
                        width: 24,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            CurrencyService.formatCurrency(controller.customerCredit,
                                decimalPlaces: 2),
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: controller.customerCredit > 0
                                  ? Colors.green.shade700
                                  : Colors.grey.shade700,
                              fontSize: 24,
                            ),
                          ),
                        ],
                      ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showResetCreditDialog(BuildContext context, PaymentsController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Reset Credit Balance',
            style: TextStyle(color: Colors.red.shade700)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Are you sure you want to reset the credit balance for this customer?',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning, color: Colors.red.shade700),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'This will permanently set the credit balance to zero. This action cannot be undone.',
                      style: TextStyle(color: Colors.red.shade900),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Customer: ${widget.selectedCustomer!.name}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(
              'Current Credit: ${CurrencyService.formatCurrency(controller.customerCredit)}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.green.shade700,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop(); // Close dialog
              try {
                await controller.resetCustomerCredit();
                if (!mounted) return;
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Credit balance has been reset to zero')),
                );
              } catch (e) {
                if (!mounted) return;
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Failed to reset credit: $e')),
                );
              }
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('RESET CREDIT'),
          ),
        ],
      ),
    );
  }

  // Method to handle deleting multiple payments
  Future<void> _deleteSelectedPayments(PaymentsController controller) async {
    // Show confirmation dialog
    final shouldDelete = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete ${controller.selectedPaymentIds.length} Payments',
            style: TextStyle(color: Colors.red.shade700)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to delete ${controller.selectedPaymentIds.length} selected payments?',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning, color: Colors.red.shade700),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'This action cannot be undone. Payments linked to bills will update the bill status.',
                      style: TextStyle(color: Colors.red.shade900),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );

    if (shouldDelete != true) return;

    try {
      await controller.deleteSelectedPayments();

      // Check if widget is still mounted before using context
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${controller.selectedPaymentIds.length} payments deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      // Check if widget is still mounted before using context
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting payments: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

// Extract payment item to a separate widget to reduce rebuilds
class _PaymentListItem extends StatelessWidget {
  final Payment payment;
  final Customer? customer;
  final Function(Payment) onDelete;
  final VoidCallback onPaymentUpdated;
  final bool isSelectionMode;
  final bool isSelected;
  final VoidCallback onToggleSelection;
  final VoidCallback onLongPress;

  const _PaymentListItem({
    required this.payment,
    required this.customer,
    required this.onDelete,
    required this.onPaymentUpdated,
    this.isSelectionMode = false,
    this.isSelected = false,
    required this.onToggleSelection,
    required this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    final formattedDate = DateFormat('dd MMM yyyy').format(payment.paymentDate);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: isSelected
                ? Colors.amber.shade500
                : payment.billId > 0
                    ? Colors.green.shade400
                    : Colors.purple.shade400,
            width: isSelected ? 2.0 : 1.5,
          ),
        ),
        color: isSelected ? Colors.amber.shade50 : null,
        child: InkWell(
          onTap: isSelectionMode
              ? () {
                  onToggleSelection();
                  // Provide haptic feedback when selecting
                  HapticFeedback.selectionClick();
                }
              : () => _showPaymentDetails(context),
          onLongPress: isSelectionMode
              ? null
              : () {
                  onLongPress();
                  // Provide haptic feedback when entering selection mode
                  HapticFeedback.heavyImpact();
                },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Row 1: Payment ID and Customer name
                Row(
                  children: [
                    // Selection checkbox when in selection mode
                    if (isSelectionMode) ...[
                      Checkbox(
                        value: isSelected,
                        onChanged: (_) => onToggleSelection(),
                        activeColor: Colors.amber.shade700,
                        checkColor: Colors.white,
                      ),
                      const SizedBox(width: 8),
                    ],

                    // Payment ID with icon
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 5),
                      decoration: BoxDecoration(
                        color: Colors.indigo.shade50,
                        borderRadius: BorderRadius.circular(5),
                        border: Border.all(color: Colors.indigo.shade200),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.payments,
                              size: 14, color: Colors.indigo.shade700),
                          const SizedBox(width: 3),
                          Text(
                            "#${payment.id.toString().padLeft(3, '0')}",
                            style: TextStyle(
                              color: Colors.indigo.shade800,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(width: 6),

                    // Customer name with icon
                    Expanded(
                      child: GestureDetector(
                        onTap: customer != null
                            ? () {
                                // Navigate to customer details if needed
                              }
                            : null,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 5),
                          decoration: BoxDecoration(
                            color: Colors.teal.shade50,
                            borderRadius: BorderRadius.circular(5),
                            border: Border.all(color: Colors.teal.shade200),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.person,
                                color: Colors.teal.shade700,
                                size: 14,
                              ),
                              const SizedBox(width: 3),
                              Expanded(
                                child: Text(
                                  customer?.name ?? 'Unknown',
                                  style: TextStyle(
                                    color: Colors.teal.shade800,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Edit button in row 1 (only when not in selection mode)
                    if (!isSelectionMode)
                      GestureDetector(
                        onTap: () => _editPayment(context),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.cyan.shade50,
                            borderRadius: BorderRadius.circular(5),
                            border: Border.all(color: Colors.cyan.shade200),
                          ),
                          child: Icon(Icons.edit,
                              color: Colors.cyan.shade700, size: 12),
                        ),
                      ),
                  ],
                ),

                const SizedBox(height: 12),

                // Row 2: Amount and delete
                Row(
                  children: [
                    // Date with icon and label
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 5),
                        decoration: BoxDecoration(
                          color: Colors.purple.shade50,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: Colors.purple.shade200),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.calendar_today,
                                size: 14, color: Colors.purple.shade700),
                            const SizedBox(width: 3),
                            Expanded(
                              child: Text(
                                formattedDate,
                                style: TextStyle(
                                  color: Colors.purple.shade800,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 6),

                    // Use AmountChip for consistency with transaction screen
                    Expanded(
                      child: AmountChip(
                        amount: payment.amount,
                        showLabel: false,
                        customColor: payment.billId > 0
                            ? Colors.green.shade700
                            : Colors.indigo.shade700,
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Delete button
                    if (!isSelectionMode)
                      GestureDetector(
                        onTap: () => onDelete(payment),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.red.shade50,
                            borderRadius: BorderRadius.circular(5),
                            border: Border.all(color: Colors.red.shade200),
                          ),
                          child: Icon(Icons.delete,
                              color: Colors.red.shade700, size: 12),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Extract all bill IDs from payment remarks and payment allocations
  Future<List<int>> _getAllLinkedBillIds(BuildContext context) async {
    final List<int> billIds = [];

    // Add the primary billId if it's valid
    if (payment.billId > 0) {
      billIds.add(payment.billId);
    }

    // Get payment allocations from database service
    try {
      final allocations =
          await DatabaseService.getPaymentAllocationsByPaymentId(payment.id);
      for (var allocation in allocations) {
        if (allocation.billId > 0) {
          final billId = allocation.billId;
          if (!billIds.contains(billId)) {
            billIds.add(billId);
          }
        }
      }
    } catch (e) {
      // Debug: Error getting payment allocations: $e
    }

    // Extract additional bill IDs from remarks
    final remarks = payment.remarks?.toLowerCase() ?? '';

    // Look for patterns like "Rs X for bill #Y"
    RegExp billRegex = RegExp(r'bill #(\d+)|bill (\d+)|bill.*?(\d+)');
    final matches = billRegex.allMatches(remarks);

    for (final match in matches) {
      // Try to get the bill ID from any capturing group
      String? billIdStr;
      for (int i = 1; i <= match.groupCount; i++) {
        if (match.group(i) != null) {
          billIdStr = match.group(i);
          break;
        }
      }

      if (billIdStr != null) {
        final billId = int.tryParse(billIdStr);
        if (billId != null && billId > 0 && !billIds.contains(billId)) {
          billIds.add(billId);
        }
      }
    }

    return billIds;
  }

  // Get bill details using bill IDs
  Future<List<Bill>> _getPaymentBillDetails(BuildContext context) async {
    final billIds = await _getAllLinkedBillIds(context);
    final bills = <Bill>[];

    for (final billId in billIds) {
      try {
        final bill = await DatabaseService.getBillWithPaymentStatus(billId);
        if (bill != null) {
          bills.add(bill);
        }
      } catch (e) {
        // Debug: Error getting bill details: $e
      }
    }

    return bills;
  }

  void _showPaymentDetails(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Payment Details'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Customer info
              Row(
                children: [
                  Icon(Icons.person, color: Colors.green.shade700, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Customer:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      customer?.name ?? 'Unknown Customer',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Payment ID row
              Row(
                children: [
                  Icon(Icons.payments, color: Colors.blue.shade700, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Payment #${payment.id}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade800,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Payment info
              Row(
                children: [
                  Icon(Icons.payments, color: Colors.indigo.shade700, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Amount:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Rs ${payment.amount.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.indigo.shade700,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Payment date
              Row(
                children: [
                  Icon(Icons.calendar_today,
                      color: Colors.purple.shade700, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Date:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    DateFormat('dd MMM yyyy').format(payment.paymentDate),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Payment method
              Row(
                children: [
                  Icon(_getPaymentMethodIcon(),
                      color: Colors.blue.shade700, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Method:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    payment.paymentMethod ?? 'Unknown',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade700,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Bills section
              FutureBuilder<List<Bill>>(
                future: _getPaymentBillDetails(context),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(
                      child: CircularProgressIndicator(strokeWidth: 2),
                    );
                  }

                  final bills = snapshot.data ?? [];

                  if (bills.isEmpty) {
                    return Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.teal.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.teal.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.account_balance_wallet,
                              color: Colors.teal.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'Credit',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.teal.shade800,
                            ),
                          ),
                        ],
                      ),
                    );
                  } else {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.receipt_long,
                                color: Color(0xFF2E7D32), size: 20),
                            const SizedBox(width: 8),
                            Text(
                              'Applied to ${bills.length} ${bills.length == 1 ? 'Bill' : 'Bills'}:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.grey.shade700,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        ...bills.map((bill) => Padding(
                              padding: const EdgeInsets.only(bottom: 8),
                              child: InkWell(
                                onTap: () {
                                  if (context.mounted) {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => BillDetailsScreen(
                                          bill: bill,
                                          customer: customer,
                                        ),
                                      ),
                                    );
                                  }
                                },
                                borderRadius: BorderRadius.circular(8),
                                child: Container(
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: bill.isPaid
                                        ? Colors.green.shade50
                                        : bill.isPartiallyPaid
                                            ? Colors.blue.shade50
                                            : Colors.red.shade50,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: bill.isPaid
                                          ? Colors.green.shade300
                                          : bill.isPartiallyPaid
                                              ? Colors.blue.shade300
                                              : Colors.red.shade300,
                                    ),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(Icons.receipt,
                                              color: bill.isPaid
                                                  ? const Color(0xFF2E7D32)
                                                  : bill.isPartiallyPaid
                                                      ? Colors.blue.shade800
                                                      : Colors.red.shade800,
                                              size: 16),
                                          const SizedBox(width: 8),
                                          Text(
                                            'Bill #${bill.id}',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: bill.isPaid
                                                  ? const Color(0xFF2E7D32)
                                                  : bill.isPartiallyPaid
                                                      ? Colors.blue.shade800
                                                      : Colors.red.shade800,
                                            ),
                                          ),
                                          const Spacer(),
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 8, vertical: 2),
                                            decoration: BoxDecoration(
                                              color: bill.isPaid
                                                  ? Colors.green.shade100
                                                  : bill.isPartiallyPaid
                                                      ? Colors.blue.shade100
                                                      : Colors.red.shade100,
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Text(
                                              bill.isPaid
                                                  ? 'Paid'
                                                  : bill.isPartiallyPaid
                                                      ? 'Partial'
                                                      : 'Unpaid',
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                                color: bill.isPaid
                                                    ? Colors.green.shade800
                                                    : bill.isPartiallyPaid
                                                        ? Colors.blue.shade800
                                                        : Colors.red.shade800,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 6),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Amount: Rs ${bill.amount.toStringAsFixed(0)}',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey.shade800,
                                            ),
                                          ),
                                          if (bill.isPartiallyPaid &&
                                              bill.partialAmount != null)
                                            Text(
                                              'Paid: Rs ${bill.partialAmount!.toStringAsFixed(0)}',
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Colors.blue.shade800,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                        ],
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.only(top: 4),
                                        child: Text(
                                          'Date: ${DateFormat('dd MMM yyyy').format(bill.billDate)}',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey.shade700,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            )),
                      ],
                    );
                  }
                },
              ),

              // Remarks section
              if (payment.remarks != null && payment.remarks!.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 8),
                Text(
                  'Remarks:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade700,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Text(
                    payment.remarks!,
                    style: TextStyle(
                      color: Colors.grey.shade800,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _editPayment(BuildContext context) {
    // Navigate to edit payment screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AutoPaymentFormScreen(
          customer: customer!,
          existingPayment: payment,
        ),
      ),
    ).then((result) {
      // If payment was updated successfully, refresh the list
      if (result == true) {
        onPaymentUpdated();
      }
    });
  }

  IconData _getPaymentMethodIcon() {
    final method = payment.paymentMethod?.toLowerCase() ?? '';

    switch (method) {
      case 'cash':
        return Icons.money;
      case 'bank transfer':
        return Icons.account_balance;
      case 'check':
        return Icons.payment;
      default:
        return Icons.payment;
    }
  }
}
