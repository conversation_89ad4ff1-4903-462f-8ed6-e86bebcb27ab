import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:tubewell_water_billing/controllers/transactions_controller.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/screens/bill_details_screen.dart';
import 'package:tubewell_water_billing/screens/customer_detail_screen.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing/widgets/empty_state_widget.dart';
import 'package:tubewell_water_billing/widgets/info_chips.dart';
import 'package:tubewell_water_billing/forms/transaction_form_screen.dart';
import 'package:tubewell_water_billing/widgets/app_drawer.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/widgets/universal_fab.dart';
import 'package:tubewell_water_billing/utils/navigation_helper.dart';

class TransactionsScreen extends StatelessWidget {
  const TransactionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => TransactionsController(),
      child: const _TransactionsView(),
    );
  }
}

class _TransactionsView extends StatefulWidget {
  const _TransactionsView();

  @override
  State<_TransactionsView> createState() => _TransactionsViewState();
}

class _TransactionsViewState extends State<_TransactionsView> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    final controller = Provider.of<TransactionsController>(context, listen: false);
    _searchController.addListener(() => controller.onSearchChanged(_searchController.text));
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.8) {
        controller.loadMoreBills();
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _deleteBill(BuildContext context, TransactionsController controller, Bill bill) async {
    final result = await controller.deleteBill(bill);
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result ? 'Water deleted successfully' : 'Failed to delete Water'),
          backgroundColor: result ? Colors.green : Colors.red,
        ),
      );
    }
  }

  void _showDeleteConfirmation(BuildContext context, TransactionsController controller, Bill bill) {
    final Customer? customer = controller.customersMap[bill.customerId];
    final String customerName = customer?.name ?? 'Unknown Customer';

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Delete Water'),
        content: Text(
            'Are you sure you want to delete this Water for $customerName?\n\n'
            'Amount: ${CurrencyService.formatCurrency(bill.amount)}\n'
            'This will also remove any associated payments.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              _deleteBill(context, controller, bill);
            },
            child: const Text('DELETE', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
  
  void _showFilterDialog(BuildContext context, TransactionsController controller) {
    DateTime? tempStartDate = controller.filterStartDate;
    DateTime? tempEndDate = controller.filterEndDate;
    bool? tempPaidStatus = controller.filterPaidStatus;
    int? tempCustomerId = controller.filterCustomerId;

    showDialog(
      context: context,
      builder: (dialogContext) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: const Text('Filter Transactions'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date range filter
                  const Text('Date Range', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () async {
                            final picked = await showDatePicker(
                              context: context,
                              initialDate: tempStartDate ?? DateTime.now(),
                              firstDate: DateTime(2020),
                              lastDate: DateTime.now(),
                            );
                            if (picked != null) {
                              setState(() => tempStartDate = picked);
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                            decoration: BoxDecoration(border: Border.all(color: Colors.grey), borderRadius: BorderRadius.circular(4)),
                            child: Text(tempStartDate != null ? DateFormat('MMM d, yyyy').format(tempStartDate!) : 'Start Date'),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: InkWell(
                          onTap: () async {
                            final picked = await showDatePicker(
                              context: context,
                              initialDate: tempEndDate ?? DateTime.now(),
                              firstDate: DateTime(2020),
                              lastDate: DateTime.now(),
                            );
                            if (picked != null) {
                              setState(() => tempEndDate = picked);
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                            decoration: BoxDecoration(border: Border.all(color: Colors.grey), borderRadius: BorderRadius.circular(4)),
                            child: Text(tempEndDate != null ? DateFormat('MMM d, yyyy').format(tempEndDate!) : 'End Date'),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Payment status filter
                  const Text('Payment Status', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      FilterChip(
                        label: const Text('All'),
                        selected: tempPaidStatus == null,
                        onSelected: (selected) => setState(() => tempPaidStatus = null),
                        backgroundColor: Colors.blue,
                        selectedColor: Colors.indigo,
                        labelStyle: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        showCheckmark: false,
                      ),
                      FilterChip(
                        label: const Text('Paid'),
                        selected: tempPaidStatus == true,
                        onSelected: (selected) => setState(() => tempPaidStatus = selected ? true : null),
                        backgroundColor: const Color(0xFF2E7D32),
                        selectedColor: const Color(0xFF1B5E20),
                        labelStyle: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        showCheckmark: false,
                      ),
                      FilterChip(
                        label: const Text('Unpaid'),
                        selected: tempPaidStatus == false,
                        onSelected: (selected) => setState(() => tempPaidStatus = selected ? false : null),
                        backgroundColor: Colors.red,
                        selectedColor: Colors.red.shade900,
                        labelStyle: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        showCheckmark: false,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Customer filter
                  const Text('Customer', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<int?>(
                    decoration: const InputDecoration(border: OutlineInputBorder(), contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8)),
                    value: tempCustomerId,
                    hint: const Text('Select Customer'),
                    items: [
                      const DropdownMenuItem<int?>(value: null, child: Text('All Customers')),
                      ...controller.customersMap.entries.map((entry) {
                        return DropdownMenuItem<int?>(value: entry.key, child: Text(entry.value.name));
                      }),
                    ],
                    onChanged: (value) => setState(() => tempCustomerId = value),
                  ),
                ],
              ),
            ),
            actions: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Wrap(
                  alignment: WrapAlignment.spaceEvenly,
                  spacing: 8.0,
                  children: [
                    ElevatedButton(
                      onPressed: () => Navigator.pop(dialogContext),
                      style: ElevatedButton.styleFrom(backgroundColor: Colors.blue, foregroundColor: Colors.white, shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)), padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10)),
                      child: const Text('Cancel'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(dialogContext);
                        controller.clearFilters();
                      },
                      style: ElevatedButton.styleFrom(backgroundColor: Colors.red, foregroundColor: Colors.white, shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)), padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10)),
                      child: const Text('Clear'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(dialogContext);
                        controller.applyFilters(
                          startDate: tempStartDate,
                          endDate: tempEndDate,
                          paidStatus: tempPaidStatus,
                          customerId: tempCustomerId,
                        );
                      },
                      style: ElevatedButton.styleFrom(backgroundColor: const Color(0xFF2E7D32), foregroundColor: Colors.white, shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)), padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10)),
                      child: const Text('Apply'),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final controller = Provider.of<TransactionsController>(context);

    return PopScope<bool>(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        final shouldExit = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Exit App'),
            content: const Text('Are you sure you want to exit the app?'),
            actions: [
              TextButton(onPressed: () => Navigator.of(context).pop(false), child: const Text('No')),
              TextButton(onPressed: () => Navigator.of(context).pop(true), style: TextButton.styleFrom(foregroundColor: Colors.red), child: const Text('Yes')),
            ],
          ),
        );

        if (shouldExit == true) {
          NavigationHelper.exitApp();
        }
      },
      child: Scaffold(
        appBar: TubewellAppBar(
          title: 'Daily Register',
          showBackButton: false,
          currentScreen: 'transactions',
          onPdfPressed: () => controller.generateTransactionsPdf(context),
        ),
        drawer: const AppDrawer(currentScreen: 'transactions'),
        body: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Search by name, ID, or amount',
                        prefixIcon: const Icon(Icons.search),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: () => _searchController.clear(),
                              )
                            : null,
                        contentPadding: const EdgeInsets.symmetric(vertical: 10),
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.0), borderSide: BorderSide(color: Colors.grey.shade300)),
                        enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(8.0), borderSide: BorderSide(color: Colors.grey.shade300)),
                        focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(8.0), borderSide: BorderSide(color: Colors.indigo.shade400, width: 2)),
                        filled: true,
                        fillColor: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    decoration: BoxDecoration(
                      color: controller.isFilteringActive ? const Color(0xFF2E7D32) : Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: IconButton(
                      icon: Icon(Icons.filter_list, color: controller.isFilteringActive ? Colors.white : Colors.grey.shade700),
                      onPressed: () => _showFilterDialog(context, controller),
                      tooltip: 'Filter',
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(8), border: Border.all(color: Colors.grey.shade300)),
                    child: PopupMenuButton<SortOption>(
                      tooltip: 'Sort by ${controller.currentSortOption.label}',
                      onSelected: (option) => controller.setSortOption(option),
                      itemBuilder: (context) => SortOption.values.map((option) {
                        final isSelected = option == controller.currentSortOption;
                        return PopupMenuItem<SortOption>(
                          value: option,
                          child: Row(
                            children: [
                              Icon(option.icon, color: isSelected ? const Color(0xFF2E7D32) : Colors.grey.shade700, size: 18),
                              const SizedBox(width: 8),
                              Expanded(child: Text(option.label, style: TextStyle(color: isSelected ? const Color(0xFF2E7D32) : Colors.black, fontWeight: isSelected ? FontWeight.bold : FontWeight.normal))),
                              if (isSelected) const Icon(Icons.check, color: Color(0xFF2E7D32), size: 18),
                            ],
                          ),
                        );
                      }).toList(),
                      icon: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.sort, color: Colors.grey.shade700, size: 20),
                          const SizedBox(width: 4),
                          Container(
                            constraints: const BoxConstraints(maxWidth: 24),
                            child: Stack(
                              alignment: Alignment.bottomRight,
                              children: [
                                const SizedBox(width: 24, height: 24),
                                Positioned(
                                  right: 0,
                                  bottom: 0,
                                  child: Container(width: 8, height: 8, decoration: const BoxDecoration(shape: BoxShape.circle, color: Color(0xFF2E7D32))),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            if (controller.isSearching || controller.isFilteringActive)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
                child: Row(
                  children: [
                    Text('Found ${controller.billsSummary['totalCount'] ?? 0} results', style: TextStyle(color: Colors.grey.shade600, fontStyle: FontStyle.italic)),
                    const Spacer(),
                    if (controller.isFilteringActive)
                      Chip(
                        label: const Text('Filters Applied'),
                        deleteIcon: const Icon(Icons.close, size: 18),
                        onDeleted: () => controller.clearFilters(),
                        backgroundColor: const Color(0xFFE8F5E9),
                        deleteIconColor: const Color(0xFF2E7D32),
                        labelStyle: const TextStyle(color: Color(0xFF2E7D32)),
                      ),
                    if (controller.isSearching)
                      TextButton(
                        onPressed: () => _searchController.clear(),
                        style: TextButton.styleFrom(foregroundColor: const Color(0xFF2E7D32)),
                        child: const Text('Clear Search'),
                      ),
                  ],
                ),
              ),
            Expanded(
              child: RefreshIndicator(
                onRefresh: controller.refreshBills,
                color: const Color(0xFF2E7D32),
                child: controller.isLoading && controller.bills.isEmpty
                    ? const Center(child: CircularProgressIndicator())
                    : controller.bills.isEmpty
                        ? EmptyStateWidget(
                            icon: controller.isSearching ? Icons.search_off : Icons.receipt_long,
                            title: controller.isSearching ? 'No Results Found' : 'No Transactions Yet',
                            message: controller.isSearching ? 'Try a different search term' : 'Record your first transaction to get started.',
                            buttonText: controller.isSearching ? 'Clear Search' : 'Add Transaction',
                            onButtonPressed: () {
                              if (controller.isSearching) {
                                _searchController.clear();
                              } else {
                                Navigator.push(context, MaterialPageRoute(builder: (context) => const TransactionFormScreen())).then((result) {
                                  if (result == true) {
                                    controller.refreshBills();
                                  }
                                });
                              }
                            },
                          )
                        : _buildScrollableContent(context, controller),
              ),
            ),
          ],
        ),
        floatingActionButton: UniversalFab(
          type: FabType.transaction,
          heroTag: 'fab-transactions',
          onResult: (result) {
            if (result) {
              controller.refreshBills();
            }
          },
        ),
      ),
    );
  }

  Widget _buildScrollableContent(BuildContext context, TransactionsController controller) {
    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        if (!controller.isLoading)
          SliverToBoxAdapter(
            child: _buildCustomSummaryCard(context, controller),
          ),
        _buildTransactionsList(context, controller),
      ],
    );
  }

  Widget _buildTransactionsList(BuildContext context, TransactionsController controller) {
    if (!controller.shouldGroupByDate) {
      return SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            if (index == controller.bills.length) {
              return const Center(child: Padding(padding: EdgeInsets.all(16.0), child: CircularProgressIndicator()));
            }
            final bill = controller.bills[index];
            final customer = controller.customersMap[bill.customerId];
            return _buildBillCard(context, controller, bill, customer);
          },
          childCount: controller.bills.length + (controller.isLoadingMore ? 1 : 0),
        ),
      );
    } else {
      return SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, dateIndex) {
            final dateKey = controller.dateKeys[dateIndex];
            final dateFormatted = DateFormat('EEEE, dd MMMM yyyy').format(DateTime.parse(dateKey));
            final billsForDate = controller.groupedBills[dateKey] ?? [];

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                    decoration: BoxDecoration(color: Colors.teal.shade700, borderRadius: BorderRadius.circular(8)),
                    child: Row(
                      children: [
                        const Icon(Icons.calendar_today, color: Colors.white, size: 18),
                        const SizedBox(width: 8),
                        Text(dateFormatted, style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 16)),
                        const Spacer(),
                        Text('${billsForDate.length} ${billsForDate.length == 1 ? 'transaction' : 'transactions'}', style: const TextStyle(color: Colors.white, fontSize: 14)),
                      ],
                    ),
                  ),
                ),
                ...billsForDate.map((bill) {
                  final customer = controller.customersMap[bill.customerId];
                  return _buildBillCard(context, controller, bill, customer);
                }),
              ],
            );
          },
          childCount: controller.dateKeys.length,
        ),
      );
    }
  }

  Widget _buildBillCard(BuildContext context, TransactionsController controller, Bill bill, Customer? customer) {
    final customerName = customer?.name ?? 'Unknown Customer';
    final billId = '#${bill.id.toString().padLeft(3, '0')}';

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: bill.isPaid ? Colors.green.shade400 : bill.isPartiallyPaid ? Colors.blue.shade400 : Colors.red.shade400,
            width: 2.0,
          ),
        ),
        child: InkWell(
          onTap: () {
            Navigator.push(context, MaterialPageRoute(builder: (context) => BillDetailsScreen(bill: bill, customer: customer))).then((_) => controller.refreshBills());
          },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                      decoration: BoxDecoration(color: Colors.indigo.shade50, borderRadius: BorderRadius.circular(8), border: Border.all(color: Colors.indigo.shade200)),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.receipt, size: 14, color: Colors.indigo.shade700),
                          const SizedBox(width: 4),
                          Text("Bill $billId", style: TextStyle(color: Colors.indigo.shade800, fontSize: 12, fontWeight: FontWeight.bold)),
                        ],
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: GestureDetector(
                        onTap: customer != null ? () => Navigator.push(context, MaterialPageRoute(builder: (context) => CustomerDetailScreen(customer: customer))) : null,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                          decoration: BoxDecoration(color: Colors.teal.shade50, borderRadius: BorderRadius.circular(8), border: Border.all(color: Colors.teal.shade200)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.person, color: Colors.teal.shade700, size: 14),
                              const SizedBox(width: 4),
                              Expanded(child: Text(customerName, style: TextStyle(color: Colors.teal.shade800, fontSize: 12, fontWeight: FontWeight.bold), overflow: TextOverflow.ellipsis, maxLines: 1)),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    GestureDetector(
                      onTap: () {
                        Navigator.push(context, MaterialPageRoute(builder: (context) => TransactionFormScreen(existingBill: bill, selectedCustomer: controller.customersMap[bill.customerId]))).then((result) {
                          if (result == true) {
                            controller.refreshBills();
                          }
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.all(5),
                        decoration: BoxDecoration(color: Colors.cyan.shade50, borderRadius: BorderRadius.circular(6), border: Border.all(color: Colors.cyan.shade200)),
                        child: Icon(Icons.edit, color: Colors.cyan.shade700, size: 14),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(child: DurationChip(hours: bill.durationHoursWhole, minutes: bill.durationMinutes, showLabel: false)),
                    const SizedBox(width: 8),
                    Expanded(child: AmountChip(amount: bill.amount, showLabel: false, customColor: bill.isPaid ? Colors.green.shade700 : bill.isPartiallyPaid ? Colors.blue.shade700 : Colors.red.shade700)),
                    const SizedBox(width: 8),
                    GestureDetector(
                      onTap: () => _showDeleteConfirmation(context, controller, bill),
                      child: Container(
                        padding: const EdgeInsets.all(5),
                        decoration: BoxDecoration(color: Colors.red.shade50, borderRadius: BorderRadius.circular(6), border: Border.all(color: Colors.red.shade200)),
                        child: Icon(Icons.delete, color: Colors.red.shade700, size: 14),
                      ),
                    ),
                  ],
                ),
                if (bill.isPartiallyPaid && bill.partialAmount != null) ...[
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(color: Colors.blue.shade50, borderRadius: BorderRadius.circular(8), border: Border.all(color: Colors.blue.shade200)),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.payment, size: 14, color: Colors.blue.shade700),
                        const SizedBox(width: 4),
                        Text('Paid: ${CurrencyService.formatCurrency(bill.partialAmount!)} of ${CurrencyService.formatCurrency(bill.amount)}', style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Colors.blue.shade700)),
                      ],
                    ),
                  ),
                ],
                if (controller.isSearching || controller.isFilteringActive || !controller.shouldGroupByDate) ...[
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Icon(Icons.event, size: 14, color: Colors.grey.shade600),
                      const SizedBox(width: 4),
                      Text(DateFormat('MMM dd, yyyy').format(bill.billDate), style: TextStyle(color: Colors.grey.shade600, fontSize: 12)),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCustomSummaryCard(BuildContext context, TransactionsController controller) {
    final int totalCount = controller.billsSummary['totalCount']?.toInt() ?? 0;
    final double totalAmount = controller.billsSummary['totalAmount']?.toDouble() ?? 0.0;
    final double paidAmount = controller.billsSummary['paidAmount']?.toDouble() ?? 0.0;
    final double unpaidAmount = controller.billsSummary['unpaidAmount']?.toDouble() ?? 0.0;

    return Container(
      margin: const EdgeInsets.fromLTRB(8, 8, 8, 0),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(color: Colors.blue.shade50, borderRadius: BorderRadius.circular(12), border: Border.all(color: Colors.blue.shade200)),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(child: Text('Transactions Summary', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.blue.shade900, fontSize: 16), overflow: TextOverflow.ellipsis)),
              Text('$totalCount transactions', style: TextStyle(color: Colors.blue.shade800, fontSize: 12)),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(color: Colors.blue.shade100, borderRadius: BorderRadius.circular(12), border: Border.all(color: Colors.blue.shade300)),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.account_balance_wallet, color: Colors.blue.shade700, size: 20),
                    const SizedBox(width: 12),
                    Text('Total Amount', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15, color: Colors.blue.shade800)),
                  ],
                ),
                const SizedBox(height: 8),
                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(CurrencyService.formatCurrency(totalAmount), style: TextStyle(fontWeight: FontWeight.bold, fontSize: 22, color: Colors.blue.shade700)),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(child: _buildFinancialSummaryItem(title: 'Paid', value: CurrencyService.formatCurrency(paidAmount), icon: Icons.check_circle, iconColor: Colors.blue.shade700, bgColor: Colors.blue.shade50, borderColor: Colors.blue.shade200, textColor: Colors.blue.shade800)),
              const SizedBox(width: 16),
              Expanded(child: _buildFinancialSummaryItem(title: 'Unpaid', value: CurrencyService.formatCurrency(unpaidAmount), icon: Icons.money_off, iconColor: Colors.red.shade700, bgColor: Colors.red.shade50, borderColor: Colors.red.shade200, textColor: Colors.red.shade800)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialSummaryItem({required String title, required String value, required IconData icon, required Color iconColor, required Color bgColor, required Color textColor, required Color borderColor}) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(color: bgColor, borderRadius: BorderRadius.circular(8), border: Border.all(color: borderColor)),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: iconColor, size: 16),
              const SizedBox(width: 6),
              Flexible(child: Text(title, style: TextStyle(color: textColor, fontSize: 13, fontWeight: FontWeight.bold), overflow: TextOverflow.ellipsis)),
            ],
          ),
          const SizedBox(height: 6),
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(value, style: TextStyle(fontWeight: FontWeight.bold, color: textColor, fontSize: 16), textAlign: TextAlign.center),
          ),
        ],
      ),
    );
  }
}
