import 'package:flutter/material.dart';
import 'package:tubewell_water_billing/services/account_service.dart';
import 'package:tubewell_water_billing/models/account.dart';
import 'package:tubewell_water_billing/widgets/bottom_navigation.dart';
import 'package:tubewell_water_billing/utils/page_transitions.dart';

class AccountSettingsController with ChangeNotifier {
  Account? _currentAccount;
  List<Account> _availableAccounts = [];
  bool _isLoading = true;

  // Getters
  Account? get currentAccount => _currentAccount;
  List<Account> get availableAccounts => _availableAccounts;
  bool get isLoading => _isLoading;

  AccountSettingsController() {
    loadAccountSettings();
  }

  Future<void> loadAccountSettings() async {
    _isLoading = true;
    notifyListeners();

    try {
      await AccountService.initialize();
      _currentAccount = AccountService.currentAccount;
      _availableAccounts = List.from(AccountService.accounts);
    } catch (e) {
      // Handle error
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addAccount(BuildContext context, String name, String? description) async {
    if (_availableAccounts.any((account) => account.name == name)) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Account with this name already exists')));
      return;
    }

    showDialog(context: context, barrierDismissible: false, builder: (context) => const AlertDialog(content: Column(mainAxisSize: MainAxisSize.min, children: [CircularProgressIndicator(), SizedBox(height: 16), Text('Creating account...')])));

    try {
      final newAccount = await AccountService.addAccount(name, description: description);
      _availableAccounts.add(newAccount);
      notifyListeners();
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Account "$name" added')));
    } catch (e) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error adding account: $e')));
    }
  }

  Future<void> switchAccount(BuildContext context, Account account) async {
    showDialog(context: context, barrierDismissible: false, builder: (context) => const AlertDialog(content: Column(mainAxisSize: MainAxisSize.min, children: [CircularProgressIndicator(), SizedBox(height: 16), Text('Switching account...')])));

    try {
      await AccountService.switchAccount(account.id);
      await loadAccountSettings();
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Switched to "${account.name}"')));
      Navigator.of(context).pushReplacementFade(const BottomNavigation(initialIndex: 3), duration: const Duration(milliseconds: 400));
    } catch (e) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error switching account: $e')));
    }
  }

  Future<void> editAccount(BuildContext context, Account account, String newName, String? newDescription) async {
    if (newName != account.name && _availableAccounts.any((a) => a.id != account.id && a.name == newName)) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Account with this name already exists')));
      return;
    }

    showDialog(context: context, barrierDismissible: false, builder: (context) => const AlertDialog(content: Column(mainAxisSize: MainAxisSize.min, children: [CircularProgressIndicator(), SizedBox(height: 16), Text('Updating account...')])));

    try {
      final updatedAccount = Account(
        id: account.id,
        name: newName,
        description: newDescription,
        createdAt: account.createdAt,
        lastAccessed: account.lastAccessed,
      );
      await AccountService.updateAccount(updatedAccount);
      await loadAccountSettings();
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Account "${updatedAccount.name}" updated')));
    } catch (e) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error updating account: $e')));
    }
  }

  Future<void> deleteAccount(BuildContext context, Account account) async {
    try {
      await AccountService.deleteAccount(account.id);
      _availableAccounts.removeWhere((a) => a.id == account.id);
      notifyListeners();
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Account "${account.name}" deleted')));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error deleting account: $e')));
    }
  }
}
