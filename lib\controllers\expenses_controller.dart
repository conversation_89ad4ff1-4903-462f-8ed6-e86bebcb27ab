import 'package:flutter/material.dart';
import 'dart:async';
import 'package:tubewell_water_billing/models/expense.dart';
import 'package:tubewell_water_billing/services/expense_service.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/services/data_change_notifier_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/universal_pdf_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_settings.dart';

// Re-define the enum or import it if it's in a shared file.
enum ExpenseSortOption {
  dateNewest(label: 'Date (Newest first)', icon: Icons.arrow_downward),
  dateOldest(label: 'Date (Oldest first)', icon: Icons.arrow_upward),
  amountHighest(label: 'Amount (Highest first)', icon: Icons.arrow_downward),
  amountLowest(label: 'Amount (Lowest first)', icon: Icons.arrow_upward),
  categoryAZ(label: 'Category (A-Z)', icon: Icons.arrow_downward),
  categoryZA(label: 'Category (Z-A)', icon: Icons.arrow_upward);

  final String label;
  final IconData icon;

  const ExpenseSortOption({
    required this.label,
    required this.icon,
  });
}

class ExpensesController with ChangeNotifier {
  // Constants
  static const int _pageSize = 20;

  // Pagination
  int _currentPage = 0;
  bool _hasMoreData = true;
  bool _isLoadingMore = false;

  // State variables
  List<Expense> _expenses = [];
  bool _isLoading = true;
  String _searchQuery = '';

  // Sort state
  ExpenseSortOption _currentSortOption = ExpenseSortOption.dateNewest;

  // Debounce search
  Timer? _debounceTimer;
  static const Duration _debounceDuration = Duration(milliseconds: 300);

  // Summary data
  Map<String, double> _expenseSummary = {
    'totalAmount': 0.0,
    'count': 0.0,
  };
  Map<String, double> _categoryTotals = {};

  // Selection mode
  bool _isSelectionMode = false;
  final Set<int> _selectedExpenseIds = <int>{};

  // Stream subscription
  late StreamSubscription<DataChangeType> _dataChangeSubscription;

  // Getters
  List<Expense> get expenses => _expenses;
  bool get isLoading => _isLoading;
  bool get isLoadingMore => _isLoadingMore;
  bool get hasMoreData => _hasMoreData;
  String get searchQuery => _searchQuery;
  ExpenseSortOption get currentSortOption => _currentSortOption;
  bool get isSortActive => _currentSortOption != ExpenseSortOption.dateNewest;
  Map<String, double> get expenseSummary => _expenseSummary;
  Map<String, double> get categoryTotals => _categoryTotals;
  bool get isSelectionMode => _isSelectionMode;
  Set<int> get selectedExpenseIds => _selectedExpenseIds;
  int get selectedExpensesCount => _selectedExpenseIds.length;

  ExpensesController() {
    _loadExpenses();
    _dataChangeSubscription =
        DataChangeNotifierService().onDataChanged.listen((changeType) {
      if (changeType == DataChangeType.expense ||
          changeType == DataChangeType.all) {
        refreshExpenses();
      }
    });
  }

  @override
  void dispose() {
    _dataChangeSubscription.cancel();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void onSearchChanged(String query) {
    if (_debounceTimer?.isActive ?? false) _debounceTimer?.cancel();

    _debounceTimer = Timer(_debounceDuration, () {
      final newQuery = query.trim().toLowerCase();
      if (_searchQuery != newQuery) {
        _searchQuery = newQuery;
        _loadExpenses(resetPagination: true);
      }
    });
  }

  Future<void> _loadExpenses({bool resetPagination = true}) async {
    if (resetPagination) {
      _isLoading = true;
      _currentPage = 0;
      notifyListeners();
    }

    try {
      final summary = await ExpenseService.getExpenseSummary();
      final categoryTotalsData = await ExpenseService.getExpensesByCategory();

      final newExpenses = await ExpenseService.getAllExpenses(
        offset: _currentPage * _pageSize,
        limit: _pageSize,
        searchQuery: _searchQuery.isNotEmpty ? _searchQuery : null,
      );

      _expenseSummary = summary;
      _categoryTotals = categoryTotalsData;

      if (resetPagination) {
        _expenses = newExpenses;
      } else {
        _expenses.addAll(newExpenses);
      }

      _sortExpenses(_expenses);

      _hasMoreData = newExpenses.length >= _pageSize;
    } catch (e) {
      // Handle error appropriately, maybe expose an error state
    } finally {
      _isLoading = false;
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  Future<void> loadMoreExpenses() async {
    if (_isLoadingMore || !_hasMoreData) return;

    _isLoadingMore = true;
    _currentPage++;
    notifyListeners();

    await _loadExpenses(resetPagination: false);
  }

  Future<void> refreshExpenses() async {
    await _loadExpenses(resetPagination: true);
  }

  void _sortExpenses(List<Expense> expenses) {
    switch (_currentSortOption) {
      case ExpenseSortOption.dateNewest:
        expenses.sort((a, b) => b.date.compareTo(a.date));
        break;
      case ExpenseSortOption.dateOldest:
        expenses.sort((a, b) => a.date.compareTo(b.date));
        break;
      case ExpenseSortOption.amountHighest:
        expenses.sort((a, b) => b.amount.compareTo(a.amount));
        break;
      case ExpenseSortOption.amountLowest:
        expenses.sort((a, b) => a.amount.compareTo(b.amount));
        break;
      case ExpenseSortOption.categoryAZ:
        expenses.sort((a, b) {
          final comparison = a.category.compareTo(b.category);
          return comparison != 0 ? comparison : b.date.compareTo(a.date);
        });
        break;
      case ExpenseSortOption.categoryZA:
        expenses.sort((a, b) {
          final comparison = b.category.compareTo(a.category);
          return comparison != 0 ? comparison : b.date.compareTo(a.date);
        });
        break;
    }
  }

  void onSortChanged(ExpenseSortOption option) {
    _currentSortOption = option;
    _sortExpenses(_expenses);
    notifyListeners();
  }

  void clearSearch() {
    _searchQuery = '';
    _loadExpenses(resetPagination: true);
  }

  Future<bool> deleteExpense(int expenseId) async {
    try {
      final deleted = await ExpenseService.deleteExpense(expenseId);
      if (deleted) {
        refreshExpenses();
      }
      return deleted;
    } catch (e) {
      return false;
    }
  }

  void toggleSelection(int expenseId) {
    if (_selectedExpenseIds.contains(expenseId)) {
      _selectedExpenseIds.remove(expenseId);
    } else {
      _selectedExpenseIds.add(expenseId);
    }

    if (_selectedExpenseIds.isEmpty) {
      _isSelectionMode = false;
    } else {
      _isSelectionMode = true;
    }
    notifyListeners();
  }

  void clearSelection() {
    _isSelectionMode = false;
    _selectedExpenseIds.clear();
    notifyListeners();
  }

  Future<int> deleteSelectedExpenses() async {
    if (_selectedExpenseIds.isEmpty) return 0;

    int successCount = 0;
    final idsToDelete = List<int>.from(_selectedExpenseIds);

    for (final id in idsToDelete) {
      final success = await ExpenseService.deleteExpense(id);
      if (success) successCount++;
    }

    clearSelection();
    refreshExpenses();
    return successCount;
  }

  Future<void> generateExpensesPdf(BuildContext context) async {
    try {
      final allExpenses = await ExpenseService.getAllExpenses(
        searchQuery: _searchQuery.isNotEmpty ? _searchQuery : null,
        offset: 0,
        limit: 1000,
      );

      _sortExpenses(allExpenses);

      final Map<String, dynamic> pdfData = {
        'expenses': allExpenses,
        'summary': _expenseSummary,
        'categoryTotals': _categoryTotals,
        'searchQuery': _searchQuery.isNotEmpty ? _searchQuery : null,
        'sortOption': _currentSortOption.label,
      };

      await UniversalPdfService.handlePdf(
        context,
        pdfData,
        autoOpen: true,
        showSaveOption: true,
        showShareOption: true,
        pdfSettings: PdfSettings.modern().copyWith(
          additionalSettings: {
            'footerText': 'Expense Report',
          },
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error generating PDF: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
