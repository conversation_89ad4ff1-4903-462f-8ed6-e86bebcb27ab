import 'package:flutter/material.dart';

/// Enum for expense categories with type safety
enum ExpenseCategory {
  electricity(value: 'Electricity', label: 'Electricity', icon: Icons.electric_bolt),
  maintenance(value: 'Maintenance', label: 'Maintenance', icon: Icons.handyman),
  repairs(value: 'Repairs', label: 'Repairs', icon: Icons.build),
  fuel(value: 'Fuel', label: 'Fuel', icon: Icons.local_gas_station),
  salaries(value: 'Salaries', label: 'Salaries', icon: Icons.payments),
  equipment(value: 'Equipment', label: 'Equipment', icon: Icons.construction),
  rent(value: 'Rent', label: 'Rent', icon: Icons.home),
  taxes(value: 'Taxes', label: 'Taxes', icon: Icons.receipt_long),
  transportation(value: 'Transportation', label: 'Transportation', icon: Icons.directions_car),
  officeSupplies(value: 'Office Supplies', label: 'Office Supplies', icon: Icons.business_center),
  other(value: 'Other', label: 'Other', icon: Icons.category);

  const ExpenseCategory({
    required this.value,
    required this.label,
    required this.icon,
  });

  final String value;
  final String label;
  final IconData icon;

  /// Get enum from string value
  static ExpenseCategory fromValue(String value) {
    return ExpenseCategory.values.firstWhere(
      (category) => category.value == value,
      orElse: () => ExpenseCategory.other,
    );
  }

  /// Get all category values as strings (for backward compatibility)
  static List<String> get allValues => ExpenseCategory.values.map((e) => e.value).toList();
}

/// Enum for payment methods with type safety
enum PaymentMethod {
  cash(value: 'Cash', label: 'Cash', icon: Icons.money),
  bankTransfer(value: 'Bank Transfer', label: 'Bank Transfer', icon: Icons.account_balance),
  check(value: 'Check', label: 'Check', icon: Icons.receipt_long),
  creditCard(value: 'Credit Card', label: 'Credit Card', icon: Icons.credit_card),
  upi(value: 'UPI', label: 'UPI', icon: Icons.qr_code),
  other(value: 'Other', label: 'Other', icon: Icons.payments);

  const PaymentMethod({
    required this.value,
    required this.label,
    required this.icon,
  });

  final String value;
  final String label;
  final IconData icon;

  /// Get enum from string value
  static PaymentMethod fromValue(String value) {
    return PaymentMethod.values.firstWhere(
      (method) => method.value == value,
      orElse: () => PaymentMethod.other,
    );
  }

  /// Get all payment method values as strings (for backward compatibility)
  static List<String> get allValues => PaymentMethod.values.map((e) => e.value).toList();
}

/// Enum for expense sorting options with type safety
enum ExpenseSortOption {
  dateNewest(value: 'dateNewest', label: 'Date (Newest first)', icon: Icons.arrow_downward),
  dateOldest(value: 'dateOldest', label: 'Date (Oldest first)', icon: Icons.arrow_upward),
  amountHighest(value: 'amountHighest', label: 'Amount (Highest first)', icon: Icons.arrow_downward),
  amountLowest(value: 'amountLowest', label: 'Amount (Lowest first)', icon: Icons.arrow_upward),
  categoryAZ(value: 'categoryAZ', label: 'Category (A-Z)', icon: Icons.arrow_downward),
  categoryZA(value: 'categoryZA', label: 'Category (Z-A)', icon: Icons.arrow_upward);

  const ExpenseSortOption({
    required this.value,
    required this.label,
    required this.icon,
  });

  final String value;
  final String label;
  final IconData icon;

  /// Get enum from string value
  static ExpenseSortOption fromValue(String value) {
    return ExpenseSortOption.values.firstWhere(
      (option) => option.value == value,
      orElse: () => ExpenseSortOption.dateNewest,
    );
  }
}

/// Enum for bill sorting options with type safety
enum BillSortOption {
  dateNewest(value: 'dateNewest', label: 'Date (Newest first)', icon: Icons.arrow_downward),
  dateOldest(value: 'dateOldest', label: 'Date (Oldest first)', icon: Icons.arrow_upward),
  amountHighest(value: 'amountHighest', label: 'Amount (Highest first)', icon: Icons.arrow_downward),
  amountLowest(value: 'amountLowest', label: 'Amount (Lowest first)', icon: Icons.arrow_upward),
  customerNameAZ(value: 'customerNameAZ', label: 'Customer Name (A-Z)', icon: Icons.arrow_downward),
  customerNameZA(value: 'customerNameZA', label: 'Customer Name (Z-A)', icon: Icons.arrow_upward),
  statusUnpaidFirst(value: 'statusUnpaidFirst', label: 'Status (Unpaid first)', icon: Icons.priority_high),
  statusPaidFirst(value: 'statusPaidFirst', label: 'Status (Paid first)', icon: Icons.check_circle_outline);

  const BillSortOption({
    required this.value,
    required this.label,
    required this.icon,
  });

  final String value;
  final String label;
  final IconData icon;

  /// Get enum from string value
  static BillSortOption fromValue(String value) {
    return BillSortOption.values.firstWhere(
      (option) => option.value == value,
      orElse: () => BillSortOption.dateNewest,
    );
  }
}

/// Enum for payment sorting options with type safety
enum PaymentSortOption {
  dateNewest(value: 'dateNewest', label: 'Date (Newest first)', icon: Icons.arrow_downward),
  dateOldest(value: 'dateOldest', label: 'Date (Oldest first)', icon: Icons.arrow_upward),
  amountHighest(value: 'amountHighest', label: 'Amount (Highest first)', icon: Icons.arrow_downward),
  amountLowest(value: 'amountLowest', label: 'Amount (Lowest first)', icon: Icons.arrow_upward),
  customerNameAZ(value: 'customerNameAZ', label: 'Customer Name (A-Z)', icon: Icons.arrow_downward),
  customerNameZA(value: 'customerNameZA', label: 'Customer Name (Z-A)', icon: Icons.arrow_upward),
  methodAZ(value: 'methodAZ', label: 'Payment Method (A-Z)', icon: Icons.arrow_downward),
  methodZA(value: 'methodZA', label: 'Payment Method (Z-A)', icon: Icons.arrow_upward);

  const PaymentSortOption({
    required this.value,
    required this.label,
    required this.icon,
  });

  final String value;
  final String label;
  final IconData icon;

  /// Get enum from string value
  static PaymentSortOption fromValue(String value) {
    return PaymentSortOption.values.firstWhere(
      (option) => option.value == value,
      orElse: () => PaymentSortOption.dateNewest,
    );
  }
}
