import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:tubewell_water_billing/forms/auto_payment_form_screen.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/models/payment.dart';
import 'package:tubewell_water_billing/screens/bill_details_screen.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/widgets/info_chips.dart';

// Extract payment item to a separate widget to reduce rebuilds
class PaymentListItem extends StatelessWidget {
  final Payment payment;
  final Customer? customer;
  final Function(Payment) onDelete;
  final VoidCallback onPaymentUpdated;
  final bool isSelectionMode;
  final bool isSelected;
  final VoidCallback onToggleSelection;
  final VoidCallback onLongPress;

  const PaymentListItem({
    super.key,
    required this.payment,
    required this.customer,
    required this.onDelete,
    required this.onPaymentUpdated,
    this.isSelectionMode = false,
    this.isSelected = false,
    required this.onToggleSelection,
    required this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    final formattedDate = DateFormat('dd MMM yyyy').format(payment.paymentDate);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: isSelected
                ? Colors.amber.shade500
                : payment.billId > 0
                    ? Colors.green.shade400
                    : Colors.purple.shade400,
            width: isSelected ? 2.0 : 1.5,
          ),
        ),
        color: isSelected ? Colors.amber.shade50 : null,
        child: InkWell(
          onTap: isSelectionMode
              ? () {
                  onToggleSelection();
                  // Provide haptic feedback when selecting
                  HapticFeedback.selectionClick();
                }
              : () => _showPaymentDetails(context),
          onLongPress: isSelectionMode
              ? null
              : () {
                  onLongPress();
                  // Provide haptic feedback when entering selection mode
                  HapticFeedback.heavyImpact();
                },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Row 1: Payment ID and Customer name
                Row(
                  children: [
                    // Selection checkbox when in selection mode
                    if (isSelectionMode) ...[
                      Checkbox(
                        value: isSelected,
                        onChanged: (_) => onToggleSelection(),
                        activeColor: Colors.amber.shade700,
                        checkColor: Colors.white,
                      ),
                      const SizedBox(width: 8),
                    ],

                    // Payment ID with icon
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 5),
                      decoration: BoxDecoration(
                        color: Colors.indigo.shade50,
                        borderRadius: BorderRadius.circular(5),
                        border: Border.all(color: Colors.indigo.shade200),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.payments,
                              size: 14, color: Colors.indigo.shade700),
                          const SizedBox(width: 3),
                          Text(
                            "#${payment.id.toString().padLeft(3, '0')}",
                            style: TextStyle(
                              color: Colors.indigo.shade800,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(width: 6),

                    // Customer name with icon
                    Expanded(
                      child: GestureDetector(
                        onTap: customer != null
                            ? () {
                                // Navigate to customer details if needed
                              }
                            : null,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 5),
                          decoration: BoxDecoration(
                            color: Colors.teal.shade50,
                            borderRadius: BorderRadius.circular(5),
                            border: Border.all(color: Colors.teal.shade200),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.person,
                                color: Colors.teal.shade700,
                                size: 14,
                              ),
                              const SizedBox(width: 3),
                              Expanded(
                                child: Text(
                                  customer?.name ?? 'Unknown',
                                  style: TextStyle(
                                    color: Colors.teal.shade800,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Edit button in row 1 (only when not in selection mode)
                    if (!isSelectionMode)
                      GestureDetector(
                        onTap: () => _editPayment(context),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.cyan.shade50,
                            borderRadius: BorderRadius.circular(5),
                            border: Border.all(color: Colors.cyan.shade200),
                          ),
                          child: Icon(Icons.edit,
                              color: Colors.cyan.shade700, size: 12),
                        ),
                      ),
                  ],
                ),

                const SizedBox(height: 12),

                // Row 2: Amount and delete
                Row(
                  children: [
                    // Date with icon and label
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 5),
                        decoration: BoxDecoration(
                          color: Colors.purple.shade50,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: Colors.purple.shade200),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.calendar_today,
                                size: 14, color: Colors.purple.shade700),
                            const SizedBox(width: 3),
                            Expanded(
                              child: Text(
                                formattedDate,
                                style: TextStyle(
                                  color: Colors.purple.shade800,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 6),

                    // Use AmountChip for consistency with transaction screen
                    Expanded(
                      child: AmountChip(
                        amount: payment.amount,
                        showLabel: false,
                        customColor: payment.billId > 0
                            ? Colors.green.shade700
                            : Colors.indigo.shade700,
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Delete button
                    if (!isSelectionMode)
                      GestureDetector(
                        onTap: () => onDelete(payment),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.red.shade50,
                            borderRadius: BorderRadius.circular(5),
                            border: Border.all(color: Colors.red.shade200),
                          ),
                          child: Icon(Icons.delete,
                              color: Colors.red.shade700, size: 12),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Extract all bill IDs from payment remarks and payment allocations
  Future<List<int>> _getAllLinkedBillIds(BuildContext context) async {
    final List<int> billIds = [];

    // Add the primary billId if it's valid
    if (payment.billId > 0) {
      billIds.add(payment.billId);
    }

    // Get payment allocations from database service
    try {
      final allocations =
          await DatabaseService.getPaymentAllocationsByPaymentId(payment.id);
      for (var allocation in allocations) {
        if (allocation.billId > 0) {
          final billId = allocation.billId;
          if (!billIds.contains(billId)) {
            billIds.add(billId);
          }
        }
      }
    } catch (e) {
      // Debug: Error getting payment allocations: $e
    }

    // Extract additional bill IDs from remarks
    final remarks = payment.remarks?.toLowerCase() ?? '';

    // Look for patterns like "Rs X for bill #Y"
    RegExp billRegex = RegExp(r'bill #(\d+)|bill (\d+)|bill.*?(\d+)');
    final matches = billRegex.allMatches(remarks);

    for (final match in matches) {
      // Try to get the bill ID from any capturing group
      String? billIdStr;
      for (int i = 1; i <= match.groupCount; i++) {
        if (match.group(i) != null) {
          billIdStr = match.group(i);
          break;
        }
      }

      if (billIdStr != null) {
        final billId = int.tryParse(billIdStr);
        if (billId != null && billId > 0 && !billIds.contains(billId)) {
          billIds.add(billId);
        }
      }
    }

    return billIds;
  }

  // Get bill details using bill IDs
  Future<List<Bill>> _getPaymentBillDetails(BuildContext context) async {
    final billIds = await _getAllLinkedBillIds(context);
    final bills = <Bill>[];

    for (final billId in billIds) {
      try {
        final bill = await DatabaseService.getBillWithPaymentStatus(billId);
        if (bill != null) {
          bills.add(bill);
        }
      } catch (e) {
        // Debug: Error getting bill details: $e
      }
    }

    return bills;
  }

  void _showPaymentDetails(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Payment Details'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Customer info
              Row(
                children: [
                  Icon(Icons.person, color: Colors.green.shade700, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Customer:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      customer?.name ?? 'Unknown Customer',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Payment ID row
              Row(
                children: [
                  Icon(Icons.payments, color: Colors.blue.shade700, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Payment #${payment.id}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade800,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Payment info
              Row(
                children: [
                  Icon(Icons.payments, color: Colors.indigo.shade700, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Amount:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Rs ${payment.amount.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.indigo.shade700,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Payment date
              Row(
                children: [
                  Icon(Icons.calendar_today,
                      color: Colors.purple.shade700, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Date:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    DateFormat('dd MMM yyyy').format(payment.paymentDate),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Payment method
              Row(
                children: [
                  Icon(_getPaymentMethodIcon(),
                      color: Colors.blue.shade700, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Method:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    payment.paymentMethod ?? 'Unknown',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade700,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Bills section
              FutureBuilder<List<Bill>>(
                future: _getPaymentBillDetails(context),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(
                      child: CircularProgressIndicator(strokeWidth: 2),
                    );
                  }

                  final bills = snapshot.data ?? [];

                  if (bills.isEmpty) {
                    return Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.teal.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.teal.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.account_balance_wallet,
                              color: Colors.teal.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'Credit',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.teal.shade800,
                            ),
                          ),
                        ],
                      ),
                    );
                  } else {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.receipt_long,
                                color: Color(0xFF2E7D32), size: 20),
                            const SizedBox(width: 8),
                            Text(
                              'Applied to ${bills.length} ${bills.length == 1 ? 'Bill' : 'Bills'}:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.grey.shade700,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        ...bills.map((bill) => Padding(
                              padding: const EdgeInsets.only(bottom: 8),
                              child: InkWell(
                                onTap: () {
                                  if (context.mounted) {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => BillDetailsScreen(
                                          bill: bill,
                                          customer: customer,
                                        ),
                                      ),
                                    );
                                  }
                                },
                                borderRadius: BorderRadius.circular(8),
                                child: Container(
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: bill.isPaid
                                        ? Colors.green.shade50
                                        : bill.isPartiallyPaid
                                            ? Colors.blue.shade50
                                            : Colors.red.shade50,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: bill.isPaid
                                          ? Colors.green.shade300
                                          : bill.isPartiallyPaid
                                              ? Colors.blue.shade300
                                              : Colors.red.shade300,
                                    ),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(Icons.receipt,
                                              color: bill.isPaid
                                                  ? const Color(0xFF2E7D32)
                                                  : bill.isPartiallyPaid
                                                      ? Colors.blue.shade800
                                                      : Colors.red.shade800,
                                              size: 16),
                                          const SizedBox(width: 8),
                                          Text(
                                            'Bill #${bill.id}',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: bill.isPaid
                                                  ? const Color(0xFF2E7D32)
                                                  : bill.isPartiallyPaid
                                                      ? Colors.blue.shade800
                                                      : Colors.red.shade800,
                                            ),
                                          ),
                                          const Spacer(),
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 8, vertical: 2),
                                            decoration: BoxDecoration(
                                              color: bill.isPaid
                                                  ? Colors.green.shade100
                                                  : bill.isPartiallyPaid
                                                      ? Colors.blue.shade100
                                                      : Colors.red.shade100,
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Text(
                                              bill.isPaid
                                                  ? 'Paid'
                                                  : bill.isPartiallyPaid
                                                      ? 'Partial'
                                                      : 'Unpaid',
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                                color: bill.isPaid
                                                    ? Colors.green.shade800
                                                    : bill.isPartiallyPaid
                                                        ? Colors.blue.shade800
                                                        : Colors.red.shade800,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 6),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Amount: Rs ${bill.amount.toStringAsFixed(0)}',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey.shade800,
                                            ),
                                          ),
                                          if (bill.isPartiallyPaid &&
                                              bill.partialAmount != null)
                                            Text(
                                              'Paid: Rs ${bill.partialAmount!.toStringAsFixed(0)}',
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Colors.blue.shade800,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                        ],
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.only(top: 4),
                                        child: Text(
                                          'Date: ${DateFormat('dd MMM yyyy').format(bill.billDate)}',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey.shade700,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            )),
                      ],
                    );
                  }
                },
              ),

              // Remarks section
              if (payment.remarks != null && payment.remarks!.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 8),
                Text(
                  'Remarks:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade700,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Text(
                    payment.remarks!,
                    style: TextStyle(
                      color: Colors.grey.shade800,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _editPayment(BuildContext context) {
    // Navigate to edit payment screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AutoPaymentFormScreen(
          customer: customer!,
          existingPayment: payment,
        ),
      ),
    ).then((result) {
      // If payment was updated successfully, refresh the list
      if (result == true) {
        onPaymentUpdated();
      }
    });
  }

  IconData _getPaymentMethodIcon() {
    final method = payment.paymentMethod?.toLowerCase() ?? '';

    switch (method) {
      case 'cash':
        return Icons.money;
      case 'bank transfer':
        return Icons.account_balance;
      case 'check':
        return Icons.payment;
      default:
        return Icons.payment;
    }
  }
}
