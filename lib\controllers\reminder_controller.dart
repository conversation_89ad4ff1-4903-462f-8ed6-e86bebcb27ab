import 'dart:async';
import 'package:flutter/material.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/services/data_change_notifier_service.dart';

class ReminderController with ChangeNotifier {
  bool _isLoading = true;
  List<Bill> _unpaidBills = [];
  Map<int, Customer> _customersMap = {};
  Map<int, List<Bill>> _customerBillsMap = {};

  String _searchQuery = '';
  bool _showPartiallyPaid = true;
  bool _showFullyUnpaid = true;

  String _sortBy = 'date'; // 'date', 'amount', 'customer'
  bool _sortAscending = false;

  Timer? _searchDebounceTimer;
  static const Duration _debounceDuration = Duration(milliseconds: 300);

  late StreamSubscription<DataChangeType> _dataChangeSubscription;

  // Getters
  bool get isLoading => _isLoading;
  String get searchQuery => _searchQuery;
  bool get showPartiallyPaid => _showPartiallyPaid;
  bool get showFullyUnpaid => _showFullyUnpaid;
  String get sortBy => _sortBy;
  bool get sortAscending => _sortAscending;
  List<MapEntry<int, List<Bill>>> get filteredAndSortedCustomerBills => _getFilteredAndSortedCustomerBills();
  Map<int, Customer> get customersMap => _customersMap;

  ReminderController() {
    loadData();
    _dataChangeSubscription =
        DataChangeNotifierService().onDataChanged.listen((changeType) {
      if (changeType == DataChangeType.bill ||
          changeType == DataChangeType.payment ||
          changeType == DataChangeType.customer ||
          changeType == DataChangeType.all) {
        loadData();
      }
    });
  }

  @override
  void dispose() {
    _dataChangeSubscription.cancel();
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  void onSearchChanged(String value) {
    if (_searchDebounceTimer?.isActive ?? false) _searchDebounceTimer?.cancel();
    _searchDebounceTimer = Timer(_debounceDuration, () {
      _searchQuery = value;
      notifyListeners();
    });
  }

  Future<void> loadData() async {
    _isLoading = true;
    notifyListeners();

    try {
      final bills = await DatabaseService.getAllBills();
      _unpaidBills = bills.where((bill) => !bill.isPaid).toList();

      final customers = await DatabaseService.getAllCustomers();
      _customersMap = {for (var c in customers) c.id: c};

      _customerBillsMap.clear();
      for (var bill in _unpaidBills) {
        if (!_customerBillsMap.containsKey(bill.customerId)) {
          _customerBillsMap[bill.customerId] = [];
        }
        _customerBillsMap[bill.customerId]!.add(bill);
      }

      _customerBillsMap.forEach((customerId, bills) {
        _sortBills(bills);
      });
    } catch (e) {
      // Handle error
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void _sortBills(List<Bill> bills) {
    switch (_sortBy) {
      case 'date':
        bills.sort((a, b) => _sortAscending ? a.billDate.compareTo(b.billDate) : b.billDate.compareTo(a.billDate));
        break;
      case 'amount':
        bills.sort((a, b) => _sortAscending ? a.outstandingAmount.compareTo(b.outstandingAmount) : b.outstandingAmount.compareTo(a.outstandingAmount));
        break;
      default:
        bills.sort((a, b) => _sortAscending ? a.billDate.compareTo(b.billDate) : b.billDate.compareTo(a.billDate));
    }
  }

  void _sortCustomers(List<MapEntry<int, List<Bill>>> entries) {
    switch (_sortBy) {
      case 'customer':
        entries.sort((a, b) {
          final customerA = _customersMap[a.key]?.name ?? '';
          final customerB = _customersMap[b.key]?.name ?? '';
          return _sortAscending ? customerA.compareTo(customerB) : customerB.compareTo(customerA);
        });
        break;
      case 'amount':
        entries.sort((a, b) {
          final totalA = a.value.fold<double>(0, (sum, bill) => sum + bill.outstandingAmount);
          final totalB = b.value.fold<double>(0, (sum, bill) => sum + bill.outstandingAmount);
          return _sortAscending ? totalA.compareTo(totalB) : totalB.compareTo(totalA);
        });
        break;
      case 'date':
        entries.sort((a, b) {
          final latestBillA = a.value.reduce((curr, next) => curr.billDate.isAfter(next.billDate) ? curr : next);
          final latestBillB = b.value.reduce((curr, next) => curr.billDate.isAfter(next.billDate) ? curr : next);
          return _sortAscending ? latestBillA.billDate.compareTo(latestBillB.billDate) : latestBillB.billDate.compareTo(latestBillA.billDate);
        });
        break;
      default:
        entries.sort((a, b) {
          final customerA = _customersMap[a.key]?.name ?? '';
          final customerB = _customersMap[b.key]?.name ?? '';
          return customerA.compareTo(customerB);
        });
    }
  }

  List<MapEntry<int, List<Bill>>> _getFilteredAndSortedCustomerBills() {
    final filteredMap = <int, List<Bill>>{};

    _customerBillsMap.forEach((customerId, bills) {
      final customer = _customersMap[customerId];
      if (customer == null) return;

      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!customer.name.toLowerCase().contains(query)) {
          return;
        }
      }

      final filteredBills = bills.where((bill) {
        if (bill.isPartiallyPaid && !_showPartiallyPaid) return false;
        if (!bill.isPartiallyPaid && !_showFullyUnpaid) return false;
        return true;
      }).toList();

      if (filteredBills.isNotEmpty) {
        filteredMap[customerId] = filteredBills;
      }
    });

    final entries = filteredMap.entries.toList();
    _sortCustomers(entries);
    return entries;
  }

  void setFilter({bool? showPartiallyPaid, bool? showFullyUnpaid}) {
    if (showPartiallyPaid != null) {
      _showPartiallyPaid = showPartiallyPaid;
    }
    if (showFullyUnpaid != null) {
      _showFullyUnpaid = showFullyUnpaid;
    }
    notifyListeners();
  }

  void setSort({required String sortBy, required bool sortAscending}) {
    _sortBy = sortBy;
    _sortAscending = sortAscending;
    notifyListeners();
  }
}
