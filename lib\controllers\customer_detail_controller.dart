import 'package:flutter/material.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/repositories/repository_service.dart';
import 'package:tubewell_water_billing/services/data_change_notifier_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/universal_pdf_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_settings.dart';
import 'package:tubewell_water_billing/widgets/reminder_dialog.dart';
import 'package:pdf/pdf.dart';
import 'dart:async';

// Import tab screen keys if they are to be used for refreshing
import 'package:tubewell_water_billing/screens/customer_detail/summary_tab.dart';
import 'package:tubewell_water_billing/screens/customer_detail/transactions_tab.dart';
import 'package:tubewell_water_billing/screens/customer_detail/payments_tab.dart';

class CustomerDetailController with ChangeNotifier {
  final Customer customer;
  late TabController tabController;

  bool _isLoadingSummary = true;
  double _totalBilledAmount = 0;
  double _totalPaidAmount = 0;
  double _netBalance = 0;

  final GlobalKey<CustomerTransactionsTabState> transactionsTabKey = GlobalKey();
  final GlobalKey<CustomerPaymentsTabState> paymentsTabKey = GlobalKey();
  final GlobalKey<CustomerSummaryTabState> summaryTabKey = GlobalKey();

  late final StreamSubscription<DataChangeType> _dataChangeSubscription;

  // Getters
  bool get isLoadingSummary => _isLoadingSummary;
  double get totalBilledAmount => _totalBilledAmount;
  double get totalPaidAmount => _totalPaidAmount;
  double get netBalance => _netBalance;

  CustomerDetailController({required this.customer, required TickerProvider vsync}) {
    tabController = TabController(length: 3, vsync: vsync);
    tabController.addListener(() {
      notifyListeners(); // Notify listeners when tab changes
    });
    loadCustomerSummary();

    _dataChangeSubscription = DataChangeNotifierService().onDataChanged.listen((changeType) {
      if (changeType == DataChangeType.payment ||
          changeType == DataChangeType.bill ||
          changeType == DataChangeType.customer ||
          changeType == DataChangeType.all) {
        refreshAllTabs();
      }
    });
  }

  @override
  void dispose() {
    tabController.dispose();
    _dataChangeSubscription.cancel();
    super.dispose();
  }

  Future<void> loadCustomerSummary() async {
    _isLoadingSummary = true;
    notifyListeners();

    try {
      final repos = RepositoryService.instance;
      final summaryData = await repos.customer.getCustomerFinancialSummary(customer.id);
      _totalBilledAmount = summaryData['totalBilled'] as double;
      _totalPaidAmount = summaryData['totalPaid'] as double;
      _netBalance = summaryData['netBalance'] as double;
    } catch (e) {
      // Handle error
    } finally {
      _isLoadingSummary = false;
      notifyListeners();
    }
  }

  void refreshAllTabs() {
    loadCustomerSummary();
    transactionsTabKey.currentState?.refreshData();
    paymentsTabKey.currentState?.refreshData();
    summaryTabKey.currentState?.refreshData();
    notifyListeners();
  }

  Future<void> handlePdfGeneration(BuildContext context) async {
    try {
      final repos = RepositoryService.instance;
      if (tabController.index == 0) {
        final bills = await repos.bill.getBillsByCustomer(customer.id);
        final pdfData = {
          'customerDetail': true,
          'customer': customer,
          'bills': bills,
          'summary': {'totalBilled': _totalBilledAmount, 'totalPaid': _totalPaidAmount, 'netBalance': _netBalance},
        };
        await UniversalPdfService.handlePdf(context, pdfData, autoOpen: true, showSaveOption: true, showShareOption: true, pdfSettings: PdfSettings.modern().copyWith(additionalSettings: {'footerText': '${customer.name} - Customer Summary'}));
      } else if (tabController.index == 1) {
        final bills = await repos.bill.getBillsByCustomer(customer.id);
        final pdfData = {
          'customerDetail': true,
          'customer': customer,
          'bills': bills,
          'summary': {'totalBilled': _totalBilledAmount, 'totalPaid': _totalPaidAmount, 'netBalance': _netBalance},
        };
        await UniversalPdfService.handlePdf(context, pdfData, autoOpen: true, showSaveOption: true, showShareOption: true, pdfSettings: PdfSettings.modern().copyWith(additionalSettings: {'footerText': '${customer.name} - Transaction Statement'}));
      } else {
        final payments = await repos.payment.getPaymentsByCustomerPaginated(customer.id, pageSize: 1000);
        final pdfData = {
          'payments': payments,
          'customer': customer,
          'summary': {'totalAmount': payments.fold(0.0, (sum, payment) => sum + payment.amount), 'count': payments.length},
          'useModernTable': true,
        };
        await UniversalPdfService.handlePdf(context, pdfData, autoOpen: true, showSaveOption: true, showShareOption: true, pdfSettings: PdfSettings.modernInvoice().copyWith(primaryColor: PdfColor.fromHex('#2E7D32'), accentColor: PdfColor.fromHex('#1565C0'), additionalSettings: {'footerText': '${customer.name} - Payment Report', 'title': 'Payment Report'}));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error generating PDF: $e'), backgroundColor: Colors.red));
    }
  }

  void showReminderDialog(BuildContext context) async {
    _isLoadingSummary = true;
    notifyListeners();
    try {
      final repos = RepositoryService.instance;
      final bills = await repos.bill.getBillsByCustomer(customer.id);
      final unpaidBills = bills.where((bill) => !bill.isPaid).toList();

      if (unpaidBills.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('No unpaid bills to send reminders for'), backgroundColor: Colors.orange));
        return;
      }
      showDialog(context: context, builder: (context) => ReminderDialog(customer: customer, bills: unpaidBills, title: 'Send Payment Reminder'));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error loading unpaid bills: $e'), backgroundColor: Colors.red));
    } finally {
      _isLoadingSummary = false;
      notifyListeners();
    }
  }
}
