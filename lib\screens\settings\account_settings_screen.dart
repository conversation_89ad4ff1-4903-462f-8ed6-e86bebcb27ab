import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tubewell_water_billing/controllers/account_settings_controller.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing/models/account.dart';

class AccountSettingsScreen extends StatelessWidget {
  const AccountSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => AccountSettingsController(),
      child: const _AccountSettingsView(),
    );
  }
}

class _AccountSettingsView extends StatelessWidget {
  const _AccountSettingsView();

  Future<void> _addAccount(BuildContext context, AccountSettingsController controller) async {
    final result = await showDialog<Map<String, String?>>(
      context: context,
      builder: (context) {
        final nameController = TextEditingController();
        final descriptionController = TextEditingController();
        return AlertDialog(
          title: const Text('Add New Account'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(controller: nameController, decoration: const InputDecoration(labelText: 'Account Name', hintText: 'Enter account name', helperText: 'Required')),
              const SizedBox(height: 16),
              TextField(controller: descriptionController, decoration: const InputDecoration(labelText: 'Description (Optional)', hintText: 'Enter account description')),
            ],
          ),
          actions: [
            TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancel')),
            TextButton(
              onPressed: () {
                if (nameController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Account name cannot be empty')));
                  return;
                }
                Navigator.of(context).pop({'name': nameController.text.trim(), 'description': descriptionController.text.trim()});
              },
              child: const Text('Add'),
            ),
          ],
        );
      },
    );

    if (result == null || result['name'] == null || result['name']!.isEmpty) return;
    await controller.addAccount(context, result['name']!, result['description']);
  }

  Future<void> _editAccount(BuildContext context, AccountSettingsController controller, Account account) async {
    final nameController = TextEditingController(text: account.name);
    final descriptionController = TextEditingController(text: account.description ?? '');

    final result = await showDialog<Map<String, String?>>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Edit Account'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(controller: nameController, decoration: const InputDecoration(labelText: 'Account Name', hintText: 'Enter account name', helperText: 'Required')),
              const SizedBox(height: 16),
              TextField(controller: descriptionController, decoration: const InputDecoration(labelText: 'Description (Optional)', hintText: 'Enter account description')),
            ],
          ),
          actions: [
            TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancel')),
            TextButton(
              onPressed: () {
                if (nameController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Account name cannot be empty')));
                  return;
                }
                Navigator.of(context).pop({'name': nameController.text.trim(), 'description': descriptionController.text.trim()});
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );

    if (result == null) return;
    await controller.editAccount(context, account, result['name']!, result['description']);
  }

  Future<void> _deleteAccount(BuildContext context, AccountSettingsController controller, Account account) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: Text('Are you sure you want to delete the account "${account.name}"? This will permanently delete all data associated with this account.'),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(false), child: const Text('Cancel')),
          TextButton(onPressed: () => Navigator.of(context).pop(true), style: TextButton.styleFrom(foregroundColor: Colors.red), child: const Text('Delete')),
        ],
      ),
    );

    if (confirmed == true) {
      await controller.deleteAccount(context, account);
    }
  }

  @override
  Widget build(BuildContext context) {
    final controller = Provider.of<AccountSettingsController>(context);

    return Scaffold(
      appBar: const TubewellAppBar(title: 'Account Settings', showPdfOption: false),
      body: controller.isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              padding: const EdgeInsets.all(16.0),
              children: [
                _buildCurrentAccount(controller),
                const SizedBox(height: 20),
                _buildAccountsList(context, controller),
                const SizedBox(height: 20),
                _buildAccountInfo(),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _addAccount(context, controller),
        backgroundColor: Colors.green,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildCurrentAccount(AccountSettingsController controller) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Current Account', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(color: Colors.blue.shade50, borderRadius: BorderRadius.circular(8), border: Border.all(color: Colors.blue.shade200)),
              child: Row(
                children: [
                  const Icon(Icons.account_circle, color: Colors.blue, size: 36),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Active Account', style: TextStyle(color: Colors.grey, fontSize: 12)),
                        Text(controller.currentAccount?.name ?? 'Default Account', style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16), maxLines: 1, overflow: TextOverflow.ellipsis),
                        if (controller.currentAccount?.description != null && controller.currentAccount!.description!.isNotEmpty)
                          Text(controller.currentAccount!.description!, style: const TextStyle(fontSize: 12)),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountsList(BuildContext context, AccountSettingsController controller) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Available Accounts', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                ElevatedButton.icon(
                  onPressed: () => _addAccount(context, controller),
                  icon: const Icon(Icons.add, size: 16),
                  label: const Text('Add New'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.green, foregroundColor: Colors.white, padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8), textStyle: const TextStyle(fontSize: 12)),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (controller.availableAccounts.isEmpty)
              Container(padding: const EdgeInsets.all(16), alignment: Alignment.center, child: const Text('No accounts available. Add one to get started.'))
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: controller.availableAccounts.length,
                itemBuilder: (context, index) {
                  final account = controller.availableAccounts[index];
                  final isCurrentAccount = controller.currentAccount?.id == account.id;
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    color: isCurrentAccount ? Colors.blue.shade50 : null,
                    elevation: isCurrentAccount ? 2 : 1,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8), side: BorderSide(color: isCurrentAccount ? Colors.blue.shade300 : Colors.grey.shade300, width: isCurrentAccount ? 1.5 : 0.5)),
                    child: ListTile(
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      title: Text(account.name, style: TextStyle(fontWeight: isCurrentAccount ? FontWeight.bold : FontWeight.normal)),
                      subtitle: account.description != null && account.description!.isNotEmpty ? Text(account.description!) : null,
                      leading: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(shape: BoxShape.circle, color: isCurrentAccount ? Colors.green.shade100 : Colors.grey.shade100),
                        child: Center(child: Icon(isCurrentAccount ? Icons.check_circle : Icons.account_circle, color: isCurrentAccount ? Colors.green : Colors.grey, size: 24)),
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(icon: const Icon(Icons.edit, color: Colors.blue, size: 20), onPressed: () => _editAccount(context, controller, account), tooltip: 'Edit Account'),
                          if (!isCurrentAccount)
                            IconButton(icon: const Icon(Icons.delete, color: Colors.red, size: 20), onPressed: () => _deleteAccount(context, controller, account), tooltip: 'Delete Account'),
                          if (!isCurrentAccount)
                            OutlinedButton(onPressed: () => controller.switchAccount(context, account), style: OutlinedButton.styleFrom(foregroundColor: Colors.blue, side: BorderSide(color: Colors.blue.shade300), padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8)), child: const Text('Switch'))
                          else
                            const Chip(label: Text('Active'), backgroundColor: Colors.green, labelStyle: TextStyle(color: Colors.white, fontSize: 12), padding: EdgeInsets.symmetric(horizontal: 8, vertical: 0)),
                        ],
                      ),
                      onTap: isCurrentAccount ? null : () => controller.switchAccount(context, account),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountInfo() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Account Information', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(color: Colors.amber.shade50, borderRadius: BorderRadius.circular(8), border: Border.all(color: Colors.amber.shade200)),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.amber, size: 16),
                      SizedBox(width: 8),
                      Text('Account Data Isolation', style: TextStyle(fontWeight: FontWeight.bold)),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text('Each account has its own separate database. When you switch accounts, you will only see data for the current account.', style: TextStyle(fontSize: 12)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
