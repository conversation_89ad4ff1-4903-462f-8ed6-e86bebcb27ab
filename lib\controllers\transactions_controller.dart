import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/repositories/repository_service.dart';
import 'package:tubewell_water_billing/services/billing_service.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/services/data_change_notifier_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/universal_pdf_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_settings.dart';

enum SortOption {
  dateNewest(label: 'Date (Newest first)', icon: Icons.arrow_downward),
  dateOldest(label: 'Date (Oldest first)', icon: Icons.arrow_upward),
  amountHighest(label: 'Amount (Highest first)', icon: Icons.arrow_downward),
  amountLowest(label: 'Amount (Lowest first)', icon: Icons.arrow_upward),
  customerNameAZ(label: 'Customer Name (A-Z)', icon: Icons.arrow_downward),
  customerNameZA(label: 'Customer Name (Z-A)', icon: Icons.arrow_upward),
  statusUnpaidFirst(label: 'Status (Unpaid first)', icon: Icons.priority_high),
  statusPaidFirst(
      label: 'Status (Paid first)', icon: Icons.check_circle_outline);

  final String label;
  final IconData icon;

  const SortOption({
    required this.label,
    required this.icon,
  });
}

class TransactionsController with ChangeNotifier {
  // Pagination
  static const int pageSize = 20;
  int _currentPage = 0;
  bool _hasMoreData = true;
  bool _isLoadingMore = false;

  List<Bill> _bills = [];
  Map<int, Customer> _customersMap = {};
  bool _isLoading = true;
  bool _isRefreshing = false;

  Map<String, List<Bill>> _groupedBills = {};
  List<String> _dateKeys = [];

  Map<String, num> _billsSummary = {
    'totalCount': 0,
    'totalAmount': 0,
    'paidAmount': 0,
    'unpaidAmount': 0,
    'paidCount': 0,
    'unpaidCount': 0
  };

  String _searchQuery = '';
  Timer? _debounceTimer;
  static const Duration _debounceDuration = Duration(milliseconds: 300);

  bool _isFilteringActive = false;
  DateTime? _filterStartDate;
  DateTime? _filterEndDate;
  bool? _filterPaidStatus;
  int? _filterCustomerId;

  SortOption _currentSortOption = SortOption.dateNewest;

  late final StreamSubscription<Currency> _currencySubscription;
  late final StreamSubscription<DataChangeType> _dataChangeSubscription;

  // Getters
  List<Bill> get bills => _bills;
  Map<int, Customer> get customersMap => _customersMap;
  bool get isLoading => _isLoading;
  bool get isLoadingMore => _isLoadingMore;
  bool get hasMoreData => _hasMoreData;
  Map<String, List<Bill>> get groupedBills => _groupedBills;
  List<String> get dateKeys => _dateKeys;
  Map<String, num> get billsSummary => _billsSummary;
  String get searchQuery => _searchQuery;
  bool get isFilteringActive => _isFilteringActive;
  SortOption get currentSortOption => _currentSortOption;
  DateTime? get filterStartDate => _filterStartDate;
  DateTime? get filterEndDate => _filterEndDate;
  bool? get filterPaidStatus => _filterPaidStatus;
  int? get filterCustomerId => _filterCustomerId;
  bool get isSearching => _searchQuery.isNotEmpty;
  bool get shouldGroupByDate => !isSearching && !isFilteringActive && (_currentSortOption == SortOption.dateNewest || _currentSortOption == SortOption.dateOldest);

  TransactionsController() {
    loadBills();
    _currencySubscription = CurrencyService.onCurrencyChanged.listen((_) {
      notifyListeners();
    });
    _dataChangeSubscription =
        DataChangeNotifierService().onDataChanged.listen((changeType) {
      if (changeType == DataChangeType.bill ||
          changeType == DataChangeType.payment ||
          changeType == DataChangeType.all) {
        refreshBills();
      }
    });
  }

  @override
  void dispose() {
    _currencySubscription.cancel();
    _dataChangeSubscription.cancel();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void onSearchChanged(String query) {
    if (_debounceTimer?.isActive ?? false) _debounceTimer?.cancel();
    _debounceTimer = Timer(_debounceDuration, () {
      _searchQuery = query;
      loadBills(resetPagination: true);
    });
  }

  Future<void> loadBills({bool resetPagination = false}) async {
    if (_isRefreshing) return;

    if (resetPagination) {
      _currentPage = 0;
    }

    _isLoading = true;
    _isRefreshing = true;
    notifyListeners();

    try {
      final repos = RepositoryService.instance;
      final newBills = await repos.bill.getBillsFiltered(
        startDate: _filterStartDate,
        endDate: _filterEndDate,
        isPaid: _filterPaidStatus,
        customerId: _filterCustomerId,
        searchQuery: _searchQuery,
        offset: _currentPage * pageSize,
        limit: pageSize,
        sortDescending: _getSortDescending(),
      );

      final summary = await repos.bill.getBillsSummary(
        startDate: _filterStartDate,
        endDate: _filterEndDate,
        isPaid: _filterPaidStatus,
        customerId: _filterCustomerId,
        searchQuery: _searchQuery,
      );

      final customerIds = newBills.map((bill) => bill.customerId).toSet().toList();
      final customers = await repos.customer.getCustomersByIds(customerIds);
      final newCustomersMap = {for (var c in customers) c.id: c};

      _hasMoreData = newBills.length >= pageSize;
      _sortBills(newBills, newCustomersMap);

      if (resetPagination) {
        _bills = newBills;
        _customersMap = newCustomersMap;
      } else {
        _bills.addAll(newBills);
        _customersMap.addAll(newCustomersMap);
      }
      
      _billsSummary = summary;

      if (shouldGroupByDate) {
        _groupBills();
      }

    } catch (e) {
      // Handle error
    } finally {
      _isLoading = false;
      _isRefreshing = false;
      notifyListeners();
    }
  }

  Future<void> loadMoreBills() async {
    if (_isLoadingMore || !_hasMoreData) return;

    _isLoadingMore = true;
    _currentPage++;
    notifyListeners();

    await loadBills(resetPagination: false);
    _isLoadingMore = false;
    notifyListeners();
  }

  Future<void> refreshBills() async {
    await loadBills(resetPagination: true);
  }

  void _sortBills(List<Bill> bills, Map<int, Customer> customers) {
    switch (_currentSortOption) {
      case SortOption.dateNewest:
        bills.sort((a, b) => b.billDate.compareTo(a.billDate));
        break;
      case SortOption.dateOldest:
        bills.sort((a, b) => a.billDate.compareTo(b.billDate));
        break;
      case SortOption.amountHighest:
        bills.sort((a, b) => b.amount.compareTo(a.amount));
        break;
      case SortOption.amountLowest:
        bills.sort((a, b) => a.amount.compareTo(b.amount));
        break;
      case SortOption.customerNameAZ:
        bills.sort((a, b) {
          final nameA = customers[a.customerId]?.name ?? '';
          final nameB = customers[b.customerId]?.name ?? '';
          return nameA.compareTo(nameB);
        });
        break;
      case SortOption.customerNameZA:
        bills.sort((a, b) {
          final nameA = customers[a.customerId]?.name ?? '';
          final nameB = customers[b.customerId]?.name ?? '';
          return nameB.compareTo(nameA);
        });
        break;
      case SortOption.statusUnpaidFirst:
        bills.sort((a, b) {
          if (a.isPaid == b.isPaid) return 0;
          return a.isPaid ? 1 : -1;
        });
        break;
      case SortOption.statusPaidFirst:
        bills.sort((a, b) {
          if (a.isPaid == b.isPaid) return 0;
          return a.isPaid ? -1 : 1;
        });
        break;
    }
  }

  void _groupBills() {
    _groupedBills.clear();
    for (var bill in _bills) {
      final dateKey = DateFormat('yyyy-MM-dd').format(bill.billDate);
      if (!_groupedBills.containsKey(dateKey)) {
        _groupedBills[dateKey] = [];
      }
      _groupedBills[dateKey]!.add(bill);
    }
    _dateKeys = _groupedBills.keys.toList();
    _dateKeys.sort((a, b) => _getSortDescending() ? b.compareTo(a) : a.compareTo(b));
  }

  bool _getSortDescending() {
    switch (_currentSortOption) {
      case SortOption.dateNewest:
      case SortOption.amountHighest:
      case SortOption.customerNameZA:
      case SortOption.statusPaidFirst:
        return true;
      default:
        return false;
    }
  }

  void setSortOption(SortOption option) {
    _currentSortOption = option;
    loadBills(resetPagination: true);
  }

  void applyFilters({
    DateTime? startDate,
    DateTime? endDate,
    bool? paidStatus,
    int? customerId,
  }) {
    _filterStartDate = startDate;
    _filterEndDate = endDate;
    _filterPaidStatus = paidStatus;
    _filterCustomerId = customerId;
    _isFilteringActive = startDate != null ||
        endDate != null ||
        paidStatus != null ||
        customerId != null;
    loadBills(resetPagination: true);
  }

  void clearFilters() {
    _filterStartDate = null;
    _filterEndDate = null;
    _filterPaidStatus = null;
    _filterCustomerId = null;
    _isFilteringActive = false;
    loadBills(resetPagination: true);
  }

  Future<bool> deleteBill(Bill bill) async {
    try {
      final result = await BillingService.deleteBill(bill);
      if (result) {
        refreshBills();
      }
      return result;
    } catch (e) {
      return false;
    }
  }

  Future<void> generateTransactionsPdf(BuildContext context) async {
    try {
      final repos = RepositoryService.instance;
      final allFilteredBills = await repos.bill.getBillsFiltered(
        startDate: _filterStartDate,
        endDate: _filterEndDate,
        isPaid: _filterPaidStatus,
        customerId: _filterCustomerId,
        searchQuery: _searchQuery,
        offset: 0,
        limit: 1000,
        sortDescending: _getSortDescending(),
      );

      final customerIds = allFilteredBills.map((bill) => bill.customerId).toSet().toList();
      final customers = await repos.customer.getCustomersByIds(customerIds);
      final customersMapForPdf = {for (var c in customers) c.id: c};

      final Map<String, dynamic> pdfData = {
        'bills': allFilteredBills,
        'customersMap': customersMapForPdf,
        'summary': _billsSummary,
        'filters': {
          'startDate': _filterStartDate,
          'endDate': _filterEndDate,
          'isPaid': _filterPaidStatus,
          'customerId': _filterCustomerId,
          'searchQuery': _searchQuery.isNotEmpty ? _searchQuery : null,
          'sortOption': _currentSortOption.label,
        },
      };

      await UniversalPdfService.handlePdf(
        context,
        pdfData,
        autoOpen: true,
        showSaveOption: true,
        showShareOption: true,
        pdfSettings: PdfSettings.modern().copyWith(
          additionalSettings: {
            'footerText': 'Transactions Report',
            'title': 'Transactions Report',
          },
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error generating PDF: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
